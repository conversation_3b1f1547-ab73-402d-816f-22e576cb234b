use argon2::{self, password_hash::SaltString, Algorithm, Version};
use base64::{engine::general_purpose, Engine as _};
use serde_json;
use tauri::AppHandle;
use zeroize::Zeroize;
// 添加用于生成联系方式相关盐的依赖
use hex;
use sha2::{Digest, Sha256};

use crate::auth::models::*;
use crate::auth::token_manager::GLOBAL_TOKEN_MANAGER;
use crate::auth::validation::*;
use crate::errors::AppError;
use crate::http::{HttpClient, HttpClientBuilder};
// 集成 crypto 模块
use crate::crypto::{
    key_derivation::{KeyDerivationParams, KeyDeriver},
    key_pair::{decrypt_private_key, encrypt_private_key, generate_keypair},
    symmetric::{decrypt_symmetric_key, encrypt_symmetric_key, generate_symmetric_key},
    vault_crypto::VaultCrypto,
    CryptoConfig, RegistrationKeychainManager, KEY_SIZE,
};

/// 密码验证错误类型
#[derive(Debug, Clone, PartialEq)]
pub enum ValidationError {
    /// 密码太短
    TooShort,
    /// 密码太长
    TooLong,
    /// 缺少大写字母
    MissingUppercase,
    /// 缺少小写字母
    MissingLowercase,
    /// 缺少数字
    MissingDigit,
    /// 缺少特殊字符
    MissingSpecialChar,
}

impl std::fmt::Display for ValidationError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            ValidationError::TooShort => write!(f, "密码长度至少需要8个字符"),
            ValidationError::TooLong => write!(f, "密码长度不能超过128个字符"),
            ValidationError::MissingUppercase => write!(f, "密码必须包含至少一个大写字母"),
            ValidationError::MissingLowercase => write!(f, "密码必须包含至少一个小写字母"),
            ValidationError::MissingDigit => write!(f, "密码必须包含至少一个数字"),
            ValidationError::MissingSpecialChar => write!(f, "密码必须包含至少一个特殊字符"),
        }
    }
}

impl std::error::Error for ValidationError {}

// # 通用密码派生和服务端提交流程文档
//
// ## 核心设计原则
//
// **完全基于密码派生**：不依赖任何本地存储（keychain、文件等），适配新设备登录场景
// **确定性服务端哈希**：同一用户的注册和登录始终生成相同的服务端哈希
// **零知识架构**：服务端永远无法获得用户的真实主密钥
//
// ## 密码派生架构
//
// ```
// 用户密码 + 联系方式
//     ↓ (确定性盐值派生)
// 中间密钥 (Intermediate Key)
//     ↓ (用途分离)
// ┌─────────────────┬─────────────────┐
// │   本地主密钥     │   服务端哈希     │
// │ (Local Master)  │ (Server Hash)   │
// │   用于本地加密   │   提交到服务端   │
// └─────────────────┴─────────────────┘
// ```
// ## 密码派生流程
// 用户密码 + 联系方式 → 中间密钥 → 本地主密钥 + 服务端哈希
//                                     ↓
//                             生成对称密钥 + 密钥对
//                                     ↓
//                             加密后提交服务端 + 原文存储keychain
//
// ## 关键特性
//
// ### 1. 确定性盐值生成
// - 基于联系方式 + 应用标识符生成确定性盐值
// - 同一用户在任何设备上都能生成相同的盐值
// - 不同用户的盐值完全不同
//
// ### 2. 双重密钥派生
// - **中间密钥**：从用户密码 + 确定性盐值派生
// - **本地主密钥**：从中间密钥 + "local" 域标识符派生
// - **服务端哈希**：从中间密钥 + "server" 域标识符派生
//
// ### 3. 跨设备一致性
// - 任何设备上输入相同的密码和联系方式
// - 都能生成相同的本地主密钥和服务端哈希
// - 无需任何本地存储或同步
//
// ## 注册流程
//
// ```rust
// let auth_service = AuthService::new(&app_handle);
// let contact = "<EMAIL>";
// let password = "UserPassword123!";
//
// // 生成所有必要的密钥和哈希
// let registration_data = auth_service.password_service()
//     .generate_registration_data(password, contact)
//     .await?;
//
// // 提交到远程服务器
// let register_request = RegisterRequest {
//     contact: contact.to_string(),
//     password_hash: registration_data.server_hash,
//     // ... 其他字段
// };
// auth_service.register_user_remote_only(register_request).await?;
// ```
//
// ## 登录流程
//
// ```rust
// let auth_service = AuthService::new(&app_handle);
// let contact = "<EMAIL>";
// let password = "UserPassword123!";  // 用户输入的密码
// let verification_code = "123456";   // 验证码
//
// // 直接从密码生成登录数据（无需本地存储）
// let login_response = auth_service
//     .login_user_universal(contact, password, verification_code)
//     .await?;
// ```
//
// ## 安全保证
//
// ### 1. 零知识架构
// - 服务端只接收到经过二次哈希的数据
// - 即使服务端数据泄露，攻击者也无法逆推出用户的真实主密钥
//
// ### 2. 前向安全性
// - 本地主密钥用于本地数据加密
// - 服务端哈希仅用于身份验证
// - 两者在密码学上完全隔离
//
// ### 3. 抗彩虹表攻击
// - 每个用户都有唯一的确定性盐值
// - 即使密码相同，不同用户的哈希也完全不同
//
// ### 4. 域隔离
// - 本地和服务端使用不同的域分离符
// - 防止跨域攻击和密钥泄露
//
// ## 测试验证
//
// ```bash
// # 验证确定性盐值生成
// cargo test test_deterministic_salt_consistency -- --nocapture
//
// # 验证跨设备一致性
// cargo test test_cross_device_consistency -- --nocapture
//
// # 验证注册登录流程
// cargo test test_universal_registration_login -- --nocapture
// ```

/// 生成确定性的用户专用盐值
///
/// 此函数为每个用户生成唯一但确定的盐值，确保：
/// 1. 同一用户在任何设备上都能生成相同的盐值
/// 2. 不同用户的盐值完全不同
/// 3. 不依赖任何本地存储或随机数
fn generate_user_deterministic_salt(contact: &str) -> Result<SaltString, AppError> {
    // 创建SHA-256哈希器
    let mut hasher = Sha256::new();

    // 添加应用标识符（防止跨应用攻击）
    hasher.update(b"secure-password-app-v2.0");
    // 添加用户唯一标识符
    hasher.update(contact.as_bytes());
    // 添加固定的域分离符
    hasher.update(b"user-deterministic-salt-v2");
    // 添加密码学常量（增强安全性）
    hasher.update(b"argon2id-kdf-salt-generation");
    // 添加应用版本标识符（支持未来的盐值升级）
    hasher.update(b"universal-cross-device-v2.0.0");

    // 获取哈希结果
    let hash_result = hasher.finalize();

    // 取前16字节作为盐（argon2推荐的最小盐长度）
    let salt_bytes = &hash_result[..16];

    // 使用无填充的base64编码，确保格式兼容SaltString
    let encoded = general_purpose::STANDARD_NO_PAD.encode(salt_bytes);

    // 创建SaltString，确保格式正确
    let salt_string = SaltString::from_b64(&encoded)
        .map_err(|e| AppError::Crypto(format!("生成确定性用户盐值格式错误: {}", e)))?;

    Ok(salt_string)
}

/// 从中间密钥派生特定用途的密钥
///
/// 使用HKDF-like的方法从中间密钥派生不同用途的密钥
fn derive_purpose_key(
    intermediate_key: &[u8; KEY_SIZE],
    purpose: &str,
    contact: &str,
) -> Result<[u8; KEY_SIZE], AppError> {
    // 创建SHA-256哈希器
    let mut hasher = Sha256::new();

    // 添加中间密钥
    hasher.update(intermediate_key);
    // 添加用途标识符
    hasher.update(purpose.as_bytes());
    // 添加用户标识符
    hasher.update(contact.as_bytes());
    // 添加固定的域分离符
    hasher.update(b"purpose-key-derivation-v2");
    // 添加应用标识符
    hasher.update(b"secure-password-app-v2.0");

    // 获取哈希结果
    let hash_result = hasher.finalize();

    // 取前32字节作为派生密钥
    let mut derived_key = [0u8; KEY_SIZE];
    derived_key.copy_from_slice(&hash_result[..KEY_SIZE]);

    Ok(derived_key)
}

/// 通用注册数据结构
#[derive(Debug, Clone)]
pub struct UniversalRegistrationData {
    /// 用于提交到服务端的哈希
    pub server_hash: String,
    /// 本地主密钥（用于本地数据加密）
    pub local_master_key: [u8; KEY_SIZE],
    /// 确定性盐值（用于验证）
    pub deterministic_salt: String,
    /// 中间密钥（临时使用，应立即清理）
    pub intermediate_key: [u8; KEY_SIZE],
}

impl Drop for UniversalRegistrationData {
    fn drop(&mut self) {
        // 安全清理敏感数据
        self.local_master_key.zeroize();
        self.intermediate_key.zeroize();
    }
}

/// 通用登录数据结构
#[derive(Debug, Clone)]
pub struct UniversalLoginData {
    /// 用于提交到服务端的哈希
    pub server_hash: String,
    /// 本地主密钥（用于本地数据加密）
    pub local_master_key: [u8; KEY_SIZE],
    /// 确定性盐值（用于验证）
    pub deterministic_salt: String,
}

impl Drop for UniversalLoginData {
    fn drop(&mut self) {
        // 安全清理敏感数据
        self.local_master_key.zeroize();
    }
}

/// 高安全性密码服务（通用版本）
///
/// 完全基于密码派生，不依赖任何本地存储，适配跨设备使用场景
pub struct PasswordService {}

impl PasswordService {
    /// 创建新的密码服务实例
    pub fn new(_app_handle: &AppHandle) -> Self {
        Self {}
    }

    /// 计算密码强度（静态方法，保持向后兼容）
    pub fn calculate_strength(password: &str) -> u8 {
        // 简单的密码强度计算
        let mut score = 0u8;

        // 长度评分
        if password.len() >= 8 {
            score += 20;
        }
        if password.len() >= 12 {
            score += 10;
        }
        if password.len() >= 16 {
            score += 10;
        }

        // 字符类型评分
        if password.chars().any(|c| c.is_lowercase()) {
            score += 15;
        }
        if password.chars().any(|c| c.is_uppercase()) {
            score += 15;
        }
        if password.chars().any(|c| c.is_ascii_digit()) {
            score += 15;
        }
        if password
            .chars()
            .any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c))
        {
            score += 15;
        }

        score.min(100)
    }

    /// 哈希密码（安全版本）（保持向后兼容）
    ///
    /// 此方法已弃用，建议使用通用方法
    pub async fn hash_password_secure(
        &self,
        password: String,
        contact: String,
    ) -> Result<SecurePasswordHash, AppError> {
        log::warn!("hash_password_secure方法已弃用，建议使用通用方法");

        // 生成注册数据
        let registration_data = self
            .generate_registration_data(password, contact.clone())
            .await?;

        // 构造兼容的返回结果
        let secure_hash = SecurePasswordHash {
            hash: hex::encode(registration_data.local_master_key),
            salt: registration_data.deterministic_salt.clone(),
            kdf_params: KeyDerivationParams {
                algorithm: Algorithm::Argon2id,
                version: Version::V0x13,
                iterations: 3,
                memory_cost: 65536,
                parallelism: 1,
                output_length: 32,
            },
            keychain_service: None,
            created_at: chrono::Utc::now(),
        };

        Ok(secure_hash)
    }

    /// 创建用户保险库加密（通用版本）
    pub async fn create_user_vault_crypto_universal(
        &self,
        contact: &str,
        password: &str,
    ) -> Result<VaultCryptoResult, AppError> {
        log::info!("创建用户保险库加密（通用版本）: {}", contact);

        // 生成注册数据
        let registration_data = self
            .generate_registration_data(password.to_string(), contact.to_string())
            .await?;

        // 使用本地主密钥生成保险库加密密钥
        let vault_key = hex::encode(&registration_data.local_master_key[..16]); // 使用前16字节
        let vault_id = format!(
            "vault_{}",
            hex::encode(&registration_data.local_master_key[16..24])
        ); // 使用8字节作为ID

        Ok(VaultCryptoResult {
            vault_id,
            vault_key,
            auto_lock_timeout: 300, // 5分钟
        })
    }

    /// 创建用户保险库加密（旧版本，保持向后兼容）
    pub async fn create_user_vault_crypto(
        &self,
        contact: &str,
        password: &str,
    ) -> Result<VaultCryptoResult, AppError> {
        log::warn!("create_user_vault_crypto方法已弃用，使用通用版本");
        self.create_user_vault_crypto_universal(contact, password)
            .await
    }

    /// 获取用户主密钥（旧版本，保持向后兼容）
    pub async fn get_user_master_key(
        &self,
        contact: &str,
    ) -> Result<Option<[u8; KEY_SIZE]>, AppError> {
        log::warn!("get_user_master_key方法已弃用，无法从本地获取主密钥");
        log::info!(
            "联系方式: {} - 由于不再依赖本地存储，无法获取主密钥",
            contact
        );
        Ok(None)
    }

    /// 生成通用注册数据
    ///
    /// 此方法从用户密码和联系方式生成所有必要的密钥和哈希
    /// 不依赖任何本地存储，适配跨设备注册场景
    pub async fn generate_registration_data(
        &self,
        password: String,
        contact: String,
    ) -> Result<UniversalRegistrationData, AppError> {
        let mut password_bytes = password.into_bytes();

        // 1. 生成确定性的用户专用盐值
        let deterministic_salt = generate_user_deterministic_salt(&contact)?;
        let salt_str = deterministic_salt.to_string();

        // 2. 使用高安全性参数派生中间密钥
        let kdf_params = KeyDerivationParams::high_security();
        let key_deriver = KeyDeriver::new(kdf_params)
            .map_err(|e| AppError::Crypto(format!("Failed to create key deriver: {}", e)))?;

        let intermediate_derived = key_deriver
            .derive(&String::from_utf8_lossy(&password_bytes), &salt_str)
            .map_err(|e| AppError::Crypto(format!("Failed to derive intermediate key: {}", e)))?;

        // 安全清理密码内存
        password_bytes.zeroize();

        let intermediate_key = intermediate_derived.clone_key();

        // 3. 从中间密钥派生本地主密钥
        let local_master_key = derive_purpose_key(&intermediate_key, "local-master", &contact)?;

        // 4. 从中间密钥派生服务端哈希密钥
        let server_key = derive_purpose_key(&intermediate_key, "server-auth", &contact)?;

        // 5. 对服务端密钥进行最终哈希处理
        let (server_hash_base64, _server_hash) = self
            .generate_final_server_hash(&server_key, &contact)
            .await?;

        log::info!(
            "Generated universal registration data for contact: {}",
            contact
        );

        Ok(UniversalRegistrationData {
            server_hash: server_hash_base64,
            local_master_key,
            deterministic_salt: salt_str,
            intermediate_key,
        })
    }

    /// 生成通用登录数据
    ///
    /// 此方法从用户密码和联系方式重新生成登录所需的数据
    /// 与注册时生成的数据完全一致，不依赖任何本地存储
    pub async fn generate_login_data(
        &self,
        password: String,
        contact: String,
    ) -> Result<UniversalLoginData, AppError> {
        let mut password_bytes = password.into_bytes();

        // 1. 生成确定性的用户专用盐值（与注册时相同）
        let deterministic_salt = generate_user_deterministic_salt(&contact)?;
        let salt_str = deterministic_salt.to_string();

        // 2. 使用相同参数派生中间密钥
        let kdf_params = KeyDerivationParams::high_security();
        let key_deriver = KeyDeriver::new(kdf_params)
            .map_err(|e| AppError::Crypto(format!("Failed to create key deriver: {}", e)))?;

        let intermediate_derived = key_deriver
            .derive(&String::from_utf8_lossy(&password_bytes), &salt_str)
            .map_err(|e| AppError::Crypto(format!("Failed to derive intermediate key: {}", e)))?;

        // 安全清理密码内存
        password_bytes.zeroize();

        let intermediate_key = intermediate_derived.clone_key();

        // 3. 从中间密钥派生本地主密钥（与注册时相同）
        let local_master_key = derive_purpose_key(&intermediate_key, "local-master", &contact)?;

        // 4. 从中间密钥派生服务端哈希密钥（与注册时相同）
        let server_key = derive_purpose_key(&intermediate_key, "server-auth", &contact)?;

        // 5. 对服务端密钥进行最终哈希处理（与注册时相同）
        let (server_hash_base64, _server_hash) = self
            .generate_final_server_hash(&server_key, &contact)
            .await?;

        log::info!("Generated universal login data for contact: {}", contact);

        Ok(UniversalLoginData {
            server_hash: server_hash_base64,
            local_master_key,
            deterministic_salt: salt_str,
        })
    }

    /// 生成最终的服务端哈希
    ///
    /// 对服务端密钥进行最终的哈希处理，生成提交到服务端的哈希值
    async fn generate_final_server_hash(
        &self,
        server_key: &[u8; KEY_SIZE],
        contact: &str,
    ) -> Result<(String, String), AppError> {
        // 修改返回类型为元组 (String, String)
        // 生成服务端专用的确定性盐值
        let server_salt = generate_deterministic_server_salt(contact)?;
        let server_salt_str = server_salt.to_string();

        // 使用平衡的KDF参数（服务端验证不需要过高的计算成本）
        let server_kdf_params = KeyDerivationParams::balanced();

        // 创建密钥派生器
        let key_deriver = KeyDeriver::new(server_kdf_params)
            .map_err(|e| AppError::Crypto(format!("Failed to create server key deriver: {}", e)))?;

        // 将服务端密钥转换为十六进制字符串作为"密码"输入
        let server_key_hex = hex::encode(server_key);

        // 对服务端密钥进行最终哈希
        let final_hash_key = key_deriver
            .derive(&server_key_hex, &server_salt_str)
            .map_err(|e| AppError::Crypto(format!("Failed to derive final server hash: {}", e)))?;

        // 只返回最终的哈希值（Base64编码）
        let server_hash_base64 = general_purpose::STANDARD.encode(final_hash_key.as_bytes());

        log::debug!("Generated secure server hash for contact: {}", contact);
        // 创建最终的服务端哈希（包含盐值信息）
        let server_hash = format!(
            "{}:{}:{}",
            server_hash_base64,
            server_salt_str,
            "balanced" // 标识使用的参数类型
        );

        Ok((server_hash_base64, server_hash)) // 返回包含两个哈希值的元组
    }

    /// 验证密码是否正确
    ///
    /// 通过重新派生密钥并比较来验证密码的正确性
    pub async fn verify_password_universal(
        &self,
        password: String,
        contact: &str,
        expected_server_hash: &str,
    ) -> Result<bool, AppError> {
        // 重新生成登录数据
        let login_data = self
            .generate_login_data(password, contact.to_string())
            .await?;

        // 比较服务端哈希
        Ok(login_data.server_hash == expected_server_hash)
    }

    /// 计算密码强度
    ///
    /// 检查密码是否符合安全要求：
    /// - 长度至少8个字符
    /// - 包含大写字母
    /// - 包含小写字母
    /// - 包含数字
    /// - 包含特殊字符
    pub fn validate_password(&self, password: &str) -> Result<(), ValidationError> {
        if password.len() < 8 {
            return Err(ValidationError::TooShort);
        }

        if password.len() > 128 {
            return Err(ValidationError::TooLong);
        }

        let has_uppercase = password.chars().any(|c| c.is_uppercase());
        let has_lowercase = password.chars().any(|c| c.is_lowercase());
        let has_digit = password.chars().any(|c| c.is_ascii_digit());
        let has_special = password
            .chars()
            .any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c));

        if !has_uppercase {
            return Err(ValidationError::MissingUppercase);
        }

        if !has_lowercase {
            return Err(ValidationError::MissingLowercase);
        }

        if !has_digit {
            return Err(ValidationError::MissingDigit);
        }

        if !has_special {
            return Err(ValidationError::MissingSpecialChar);
        }

        Ok(())
    }

    /// 创建用户的 VaultCrypto 实例（使用通用方法）
    pub async fn create_user_vault_crypto_with_vault(
        &self,
        contact: &str,
        password: &str,
    ) -> Result<VaultCrypto, AppError> {
        // 生成登录数据
        let login_data = self
            .generate_login_data(password.to_string(), contact.to_string())
            .await?;

        // 创建高安全性配置（禁用keychain依赖）
        let crypto_config = CryptoConfig {
            argon2_iterations: 5,         // 高安全性迭代次数
            argon2_memory: 131072,        // 128 MB 内存使用
            argon2_parallelism: 8,        // 8 线程并行
            enable_keychain: false,       // 禁用密钥链依赖
            auto_lock_timeout: Some(600), // 10分钟自动锁定
        };

        // 创建 VaultCrypto 实例
        let vault_crypto = VaultCrypto::new(crypto_config)
            .map_err(|e| AppError::Crypto(format!("Failed to create VaultCrypto: {}", e)))?;

        // 使用本地主密钥解锁保险库
        let master_key_hex = hex::encode(&login_data.local_master_key);
        vault_crypto
            .unlock(&master_key_hex, &login_data.deterministic_salt)
            .await
            .map_err(|e| AppError::Crypto(format!("Failed to unlock vault: {}", e)))?;

        Ok(vault_crypto)
    }

    // === 保持向后兼容的旧方法 ===

    /// 生成服务端特定的确定性盐值（保持向后兼容）
    pub async fn generate_server_password_hash(
        &self,
        master_key: &[u8; KEY_SIZE],
        contact: &str,
    ) -> Result<String, AppError> {
        let (_server_hash_base64, server_hash) =
            self.generate_final_server_hash(master_key, contact).await?;

        Ok(server_hash)
    }

    /// 验证服务端密码哈希（保持向后兼容）
    pub async fn verify_server_password_hash(
        &self,
        master_key: &[u8; KEY_SIZE],
        server_hash: &str,
    ) -> Result<bool, AppError> {
        // 解析服务端哈希格式
        let parts: Vec<&str> = server_hash.split(':').collect();
        if parts.len() != 3 {
            return Err(AppError::Crypto("Invalid server hash format".to_string()));
        }

        let stored_hash = parts[0];
        let salt_str = parts[1];
        let _params_type = parts[2]; // 可用于未来的参数类型验证

        // 使用相同的参数重新计算哈希
        let server_kdf_params = KeyDerivationParams::balanced();
        let key_deriver = KeyDeriver::new(server_kdf_params)
            .map_err(|e| AppError::Crypto(format!("Failed to create server key deriver: {}", e)))?;

        let master_key_hex = hex::encode(master_key);
        let computed_hash_key = key_deriver
            .derive(&master_key_hex, salt_str)
            .map_err(|e| AppError::Crypto(format!("Failed to derive server hash: {}", e)))?;

        // 解码存储的哈希
        let stored_hash_bytes = general_purpose::STANDARD
            .decode(stored_hash)
            .map_err(|e| AppError::Crypto(format!("Failed to decode stored server hash: {}", e)))?;

        // 使用常时间比较
        Ok(computed_hash_key.verify(&stored_hash_bytes))
    }
}

/// 生成结合联系方式的安全盐
///
/// 此函数结合联系方式和加密安全的随机数据生成唯一的盐值，
/// 确保同一个密码在不同用户间有不同的盐值，提高安全性
#[cfg(test)]
fn generate_contact_based_salt(contact: &str) -> Result<SaltString, AppError> {
    use argon2::password_hash::rand_core::{OsRng, RngCore};

    // 生成32字节的加密安全随机数据
    let mut random_bytes = [0u8; 32];
    OsRng.fill_bytes(&mut random_bytes);

    // 创建SHA-256哈希器
    let mut hasher = Sha256::new();

    // 添加联系方式（邮箱或手机号）
    hasher.update(contact.as_bytes());
    // 添加随机数据
    hasher.update(&random_bytes);
    // 添加固定的域分离符，防止不同用途的盐值冲突
    hasher.update(b"secure-password-salt-v1");
    // 添加时间戳增强随机性
    hasher.update(
        &std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_nanos()
            .to_le_bytes(),
    );

    // 获取哈希结果
    let hash_result = hasher.finalize();

    // 取前16字节作为盐（argon2推荐的最小盐长度）
    let salt_bytes = &hash_result[..16];

    // 使用无填充的base64编码，确保格式兼容SaltString
    let encoded = general_purpose::STANDARD_NO_PAD.encode(salt_bytes);

    // 创建SaltString，确保格式正确
    let salt_string = SaltString::from_b64(&encoded)
        .map_err(|e| AppError::Crypto(format!("生成盐值格式错误: {}", e)))?;

    Ok(salt_string)
}

/// 生成服务端特定的确定性盐值
///
/// 此函数为服务端提交生成确定性的盐值，对于同一用户始终生成相同的盐值
/// 使用应用标识符、联系方式和固定的域分离符生成唯一但确定的盐值
fn generate_deterministic_server_salt(contact: &str) -> Result<SaltString, AppError> {
    // 创建SHA-256哈希器
    let mut hasher = Sha256::new();

    // 添加应用标识符（防止跨应用攻击）
    hasher.update(b"secure-password-app-v1.0");
    // 添加服务端域名（防止跨服务攻击）
    hasher.update(b"server-submission-deterministic");
    // 添加联系方式（确保用户唯一性）
    hasher.update(contact.as_bytes());
    // 添加固定的域分离符（防止不同用途的盐值冲突）
    hasher.update(b"server-hash-salt-deterministic-v1");
    // 添加固定的应用版本标识符（支持未来的盐值升级）
    hasher.update(b"app-version-1.0.0");

    // 获取哈希结果
    let hash_result = hasher.finalize();

    // 取前16字节作为盐（argon2推荐的最小盐长度）
    let salt_bytes = &hash_result[..16];

    // 使用无填充的base64编码，确保格式兼容SaltString
    let encoded = general_purpose::STANDARD_NO_PAD.encode(salt_bytes);

    // 创建SaltString，确保格式正确
    let salt_string = SaltString::from_b64(&encoded)
        .map_err(|e| AppError::Crypto(format!("生成确定性服务端盐值格式错误: {}", e)))?;

    Ok(salt_string)
}

/// 认证服务（综合服务）
///
/// 新增通用方法，完全基于密码派生，不依赖任何本地存储
pub struct AuthService {
    password_service: PasswordService,
    remote_service: RemoteAuthService,
}

impl AuthService {
    /// 创建新的认证服务实例
    pub fn new(app_handle: &AppHandle) -> Self {
        Self {
            password_service: PasswordService::new(app_handle),
            remote_service: RemoteAuthService::default(),
        }
    }

    /// 生成加密密钥对
    ///
    /// 使用本地主密钥生成对称密钥、公钥和私钥
    /// 对称密钥和私钥使用本地主密钥加密
    async fn generate_encryption_keys(
        &self,
        local_master_key: &[u8; KEY_SIZE],
    ) -> Result<(String, String, String, [u8; KEY_SIZE], String), AppError> {
        log::info!("开始生成加密密钥对");

        // 1. 生成对称密钥（用于数据加密）
        let symmetric_key = generate_symmetric_key()
            .map_err(|e| AppError::Crypto(format!("生成对称密钥失败: {}", e)))?;

        // 2. 生成Ed25519密钥对（用于分享和签名）
        let keypair =
            generate_keypair().map_err(|e| AppError::Crypto(format!("生成密钥对失败: {}", e)))?;

        // 3. 使用本地主密钥加密对称密钥
        let encrypted_symmetric_key = encrypt_symmetric_key(&symmetric_key, local_master_key)
            .map_err(|e| AppError::Crypto(format!("加密对称密钥失败: {}", e)))?;

        // 4. 使用本地主密钥加密私钥
        let encrypted_private_key = encrypt_private_key(&keypair.private_key, local_master_key)
            .map_err(|e| AppError::Crypto(format!("加密私钥失败: {}", e)))?;

        // 5. 公钥不需要加密，直接使用
        let public_key = keypair.public_key.clone();
        let private_key_original = keypair.private_key;

        log::info!("加密密钥对生成完成");
        log::debug!("对称密钥长度: {} 字节", symmetric_key.len());
        log::debug!("公钥长度: {} 字符", public_key.len());
        log::debug!("私钥已加密并准备存储");

        // 返回: (加密的对称密钥, 公钥, 加密的私钥, 原始对称密钥, 原始私钥)
        Ok((
            encrypted_symmetric_key,
            public_key,
            encrypted_private_key,
            symmetric_key,
            private_key_original,
        ))
    }

    /// 将密钥存储到keychain
    ///
    /// 在注册成功后，将本地主密钥、对称密钥原文、公私钥对原文存储到keychain
    async fn store_keys_to_keychain(
        &self,
        contact: &str,
        local_master_key: &[u8; KEY_SIZE],
        symmetric_key: &[u8; KEY_SIZE],
        public_key: &str,
        private_key: &str,
    ) -> Result<(), AppError> {
        log::info!("开始存储密钥到keychain: {}", contact);

        // 使用 RegistrationKeychainManager 存储密钥
        let keychain_manager = RegistrationKeychainManager::get_or_create(contact).await;

        // 1. 存储本地主密钥
        keychain_manager
            .store_master_key(local_master_key)
            .await
            .map_err(|e| AppError::Crypto(format!("存储主密钥到keychain失败: {}", e)))?;

        // 2. 存储对称密钥原文
        keychain_manager
            .store_symmetric_key(symmetric_key)
            .await
            .map_err(|e| AppError::Crypto(format!("存储对称密钥到keychain失败: {}", e)))?;

        // 3. 存储公钥
        keychain_manager
            .store_public_key(public_key)
            .await
            .map_err(|e| AppError::Crypto(format!("存储公钥到keychain失败: {}", e)))?;

        // 4. 存储私钥原文
        keychain_manager
            .store_private_key(private_key)
            .await
            .map_err(|e| AppError::Crypto(format!("存储私钥到keychain失败: {}", e)))?;

        log::info!("所有密钥已成功存储到keychain: {}", contact);
        log::debug!("存储的密钥类型: master_key, symmetric_key, public_key, private_key");

        Ok(())
    }

    /// 通用注册用户（完全基于密码派生）
    ///
    /// 此方法不依赖任何本地存储，适配跨设备注册场景
    /// 从用户密码和联系方式生成所有必要的密钥和哈希
    pub async fn register_user_universal(
        &self,
        contact: String,
        password: String,
        nickname: String,
        verification_code: String,
        registration_type: RegistrationType,
        password_hint: Option<String>,
    ) -> Result<(RemoteRegisterResponse, UniversalRegistrationData), AppError> {
        log::info!("开始通用注册用户: {} - {}", nickname, contact);

        // 1. 生成注册数据（包含本地主密钥和服务端哈希）
        let registration_data = self
            .password_service
            .generate_registration_data(password, contact.clone())
            .await?;

        // 2. 生成加密密钥对（使用本地主密钥）
        let (symmetric_key, public_key, private_key, symmetric_key_original, private_key_original) =
            self.generate_encryption_keys(&registration_data.local_master_key)
                .await?;

        // 3. 构建注册请求
        let register_request = RegisterRequest {
            nickname,
            contact: contact.clone(),
            registration_type,
            password_hash: registration_data.server_hash.clone(),
            password_hint,
            symmetric_key,
            public_key: public_key.clone(),
            private_key,
            verification_code,
        };

        // 4. 提交到远程服务端
        let remote_response = self
            .remote_service
            .register_user_remote(register_request)
            .await?;

        if remote_response.code == 0 {
            log::info!("通用注册成功: {}", contact);

            // 5. 注册成功后，将密钥存储到keychain
            if let Err(e) = self
                .store_keys_to_keychain(
                    &contact,
                    &registration_data.local_master_key,
                    &symmetric_key_original,
                    &public_key,
                    &private_key_original,
                )
                .await
            {
                log::warn!("存储密钥到keychain失败: {} - 这不会影响注册流程", e);
                // 注意：keychain存储失败不会影响注册成功，只是记录警告
            } else {
                log::info!("密钥已成功存储到keychain: {}", contact);
            }
        } else {
            log::warn!(
                "通用注册失败: {} (code: {})",
                remote_response.message,
                remote_response.code
            );
        }

        Ok((remote_response, registration_data))
    }

    /// 通用登录用户（完全基于密码派生）
    ///
    /// 此方法不依赖任何本地存储，适配跨设备登录场景
    /// 从用户密码和联系方式重新生成登录所需的数据
    pub async fn login_user_universal(
        &self,
        contact: String,
        password: String,
        verification_code: String,
    ) -> Result<(RemoteLoginResponse, UniversalLoginData), AppError> {
        log::info!("开始通用登录用户: {}", contact);

        // 1. 生成登录数据（与注册时完全一致）
        let login_data = self
            .password_service
            .generate_login_data(password, contact.clone())
            .await?;

        log::info!("密码处理完成，生成服务端哈希用于登录: {}", contact);

        // 2. 调用远程登录API
        let login_response = self
            .remote_service
            .login_user_remote(
                contact.clone(),
                login_data.server_hash.clone(),
                verification_code,
            )
            .await?;

        if login_response.code == 0 {
            log::info!("通用登录成功: {}", login_response.message);

            // 3. 登录成功后，检查并更新keychain中的主密钥
            self.check_and_update_keychain_master_key(&contact, &login_data.local_master_key)
                .await?;

            // 4. 登录成功后，自动同步远程密钥到本地
            log::info!("开始同步远程密钥到本地: {}", contact);
            match self
                .remote_service
                .sync_keys_with_remote(&contact, &login_data.local_master_key)
                .await
            {
                Ok(sync_result) => {
                    log::info!("密钥同步成功: {}", sync_result.message);
                    if sync_result.has_conflicts {
                        log::warn!(
                            "检测到密钥冲突，已更新本地密钥: {:?}",
                            sync_result.updated_keys
                        );
                    }
                }
                Err(e) => {
                    log::warn!("密钥同步失败: {} - 这不会影响登录流程", e);
                    // 注意：密钥同步失败不会影响登录成功，只是记录警告
                }
            }
        } else {
            log::warn!(
                "通用登录失败: {} (code: {})",
                login_response.message,
                login_response.code
            );
        }

        Ok((login_response, login_data))
    }

    /// 检查并更新keychain中的主密钥
    ///
    /// 如果本地keychain没有存储主密钥，或者存储的主密钥和本次登录成功使用的主密钥不同，
    /// 则将本次登录成功使用的主密钥存储在keychain中
    async fn check_and_update_keychain_master_key(
        &self,
        contact: &str,
        current_master_key: &[u8; KEY_SIZE],
    ) -> Result<(), AppError> {
        log::info!("检查并更新keychain中的主密钥: {}", contact);

        // 创建keychain管理器
        let keychain_manager = RegistrationKeychainManager::get_or_create(contact).await;

        // 尝试获取keychain中存储的主密钥
        match keychain_manager.get_master_key().await {
            Ok(stored_master_key) => {
                // keychain中有主密钥，检查是否与当前主密钥一致
                if stored_master_key == *current_master_key {
                    log::info!(
                        "keychain中的主密钥与当前登录主密钥一致，无需更新: {}",
                        contact
                    );
                } else {
                    log::warn!(
                        "keychain中的主密钥与当前登录主密钥不一致，更新keychain: {}",
                        contact
                    );

                    // 更新keychain中的主密钥
                    keychain_manager
                        .store_master_key(current_master_key)
                        .await
                        .map_err(|e| AppError::Crypto(format!("更新keychain主密钥失败: {}", e)))?;

                    log::info!("keychain主密钥已更新为当前登录主密钥: {}", contact);
                }
            }
            Err(e) => {
                // keychain中没有主密钥或读取失败，存储当前主密钥
                log::info!(
                    "keychain中没有主密钥或读取失败 ({}), 存储当前登录主密钥: {}",
                    e,
                    contact
                );

                keychain_manager
                    .store_master_key(current_master_key)
                    .await
                    .map_err(|e| AppError::Crypto(format!("存储主密钥到keychain失败: {}", e)))?;

                log::info!("当前登录主密钥已存储到keychain: {}", contact);
            }
        }

        Ok(())
    }

    /// 验证用户密码（通用版本）
    ///
    /// 通过重新派生密钥并与预期的服务端哈希比较来验证密码
    pub async fn verify_password_universal(
        &self,
        contact: &str,
        password: String,
        expected_server_hash: &str,
    ) -> Result<bool, AppError> {
        log::info!("验证通用密码: {}", contact);

        let is_valid = self
            .password_service
            .verify_password_universal(password, contact, expected_server_hash)
            .await?;

        log::info!("通用密码验证结果: {} - {}", contact, is_valid);
        Ok(is_valid)
    }

    // === 保持向后兼容的旧方法 ===

    /// 验证注册请求（基本验证，不包括本地存储检查）
    pub async fn validate_register_request_basic(
        &self,
        request: &RegisterRequest,
    ) -> Result<(), AppError> {
        // 验证用户名
        validate_username(&request.nickname).map_err(|e| AppError::InvalidInput(e.to_string()))?;

        // 验证联系方式
        validate_contact(&request.contact, &request.registration_type)
            .map_err(|e| AppError::InvalidInput(e.to_string()))?;

        // 验证验证码格式
        validate_verification_code(&request.verification_code)
            .map_err(|e| AppError::InvalidInput(e.to_string()))?;

        // 验证密码提示词
        if let Some(hint) = &request.password_hint {
            if hint.len() > 100 {
                return Err(AppError::InvalidInput(
                    "密码提示词长度不能超过100个字符".to_string(),
                ));
            }
        }

        // 验证必需的加密字段
        if request.symmetric_key.is_empty() {
            return Err(AppError::InvalidInput("对称密钥不能为空".to_string()));
        }

        if request.public_key.is_empty() {
            return Err(AppError::InvalidInput("公钥不能为空".to_string()));
        }

        if request.private_key.is_empty() {
            return Err(AppError::InvalidInput("私钥不能为空".to_string()));
        }

        Ok(())
    }

    /// 发送验证码（通过远程服务）
    pub async fn send_verification_code(
        &self,
        contact: String,
        contact_type: RegistrationType,
    ) -> Result<(), AppError> {
        // 验证联系方式格式
        validate_contact(&contact, &contact_type)
            .map_err(|e| AppError::InvalidInput(e.to_string()))?;

        // 通过远程服务发送验证码
        self.remote_service
            .send_verification_code_remote(contact, contact_type)
            .await?;

        Ok(())
    }

    /// 注册用户（仅远程注册）
    ///
    /// 注册逻辑完全由远程服务端处理，客户端只负责数据验证和发送请求
    pub async fn register_user_remote_only(
        &self,
        request: RegisterRequest,
    ) -> Result<RemoteRegisterResponse, AppError> {
        // 基本验证（不包括本地存储相关的验证）
        self.validate_register_request_basic(&request).await?;

        log::info!(
            "开始远程注册用户: {} - {}",
            request.nickname,
            request.contact
        );

        // 直接调用远程注册服务
        let remote_service = RemoteAuthService::default();
        let response = remote_service.register_user_remote(request).await?;

        if response.code == 0 {
            log::info!("远程注册成功: {}", response.message);
        } else {
            log::warn!(
                "远程注册失败: {} (code: {})",
                response.message,
                response.code
            );
        }

        Ok(response)
    }

    /// 获取密码服务
    pub fn password_service(&self) -> &PasswordService {
        &self.password_service
    }

    /// 获取远程认证服务
    pub fn remote_service(&self) -> &RemoteAuthService {
        &self.remote_service
    }

    /// 处理用户登录流程（旧版本，保持向后兼容）
    ///
    /// 此方法处理完整的登录流程：
    /// 1. 验证用户输入的密码
    /// 2. 生成服务端提交哈希
    /// 3. 调用远程登录API
    /// 4. 返回登录结果
    pub async fn login_user(
        &self,
        contact: String,
        password: String,
        verification_code: String,
    ) -> Result<RemoteLoginResponse, AppError> {
        log::info!("开始用户登录流程（旧版本）: {}", contact);

        // 使用通用方法处理登录
        let (login_response, _login_data) = self
            .login_user_universal(contact, password, verification_code)
            .await?;

        Ok(login_response)
    }

    /// 处理用户登录流程（当已知本地哈希时）（旧版本，保持向后兼容）
    ///
    /// 当调用方已经有本地存储的密码哈希时，可以使用此方法
    pub async fn login_user_with_hash(
        &self,
        contact: String,
        password: String,
        verification_code: String,
        _local_hash: &SecurePasswordHash,
    ) -> Result<RemoteLoginResponse, AppError> {
        log::info!("开始用户登录流程（使用已知哈希，旧版本）: {}", contact);

        // 忽略本地哈希，使用通用方法处理登录
        let (login_response, _login_data) = self
            .login_user_universal(contact, password, verification_code)
            .await?;

        Ok(login_response)
    }

    /// 验证本地密码（不涉及远程登录）（旧版本，保持向后兼容）
    ///
    /// 此方法仅验证用户输入的密码是否与本地存储的密码匹配
    pub async fn verify_local_password(
        &self,
        contact: &str,
        _password: String,
    ) -> Result<bool, AppError> {
        log::info!("验证本地密码（旧版本）: {}", contact);

        // 由于不再依赖本地存储，这个方法无法实现
        // 返回false表示无法验证
        log::warn!("旧版本的本地密码验证已弃用，请使用通用验证方法");
        Ok(false)
    }
}

/// 安全密码哈希结果（保持向后兼容）
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct SecurePasswordHash {
    /// Base64编码的密钥哈希
    pub hash: String,
    /// 盐值
    pub salt: String,
    /// 密钥派生参数
    pub kdf_params: KeyDerivationParams,
    /// 密钥链服务名称（可选）
    pub keychain_service: Option<String>,
    /// 创建时间
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// 登录密码处理结果（保持向后兼容）
#[derive(Debug, Clone)]
pub struct LoginPasswordResult {
    /// 用于提交到服务端的哈希
    pub server_hash: String,
    /// 本地存储的密码哈希信息
    pub local_hash: SecurePasswordHash,
    /// 派生的主密钥（用于后续的加密操作）
    pub master_key: [u8; KEY_SIZE],
}

/// 远程认证服务
/// 负责与远程服务端进行认证相关的通信
pub struct RemoteAuthService {
    /// 普通 HTTP 客户端（用于注册、登录等不需要认证的请求）
    http_client: HttpClient,
    /// 带认证的 HTTP 客户端（用于需要 Authorization header 的请求）
    auth_http_client: HttpClient,
    /// 认证拦截器（用于管理 token）
    auth_interceptor: std::sync::Arc<crate::http::interceptor::AuthInterceptor>,
    base_url: String,
}

impl RemoteAuthService {
    /// 创建新的远程认证服务实例
    pub fn new(base_url: impl Into<String>) -> Self {
        let base_url = base_url.into();

        // 普通的 HTTP 客户端（用于注册、登录等不需要认证的请求）
        let http_client = HttpClientBuilder::new()
            .base_url(&base_url)
            .timeout(std::time::Duration::from_secs(30))
            .build();

        // 创建认证拦截器
        let auth_interceptor =
            std::sync::Arc::new(crate::http::interceptor::AuthInterceptor::new());

        // 带认证的 HTTP 客户端（用于需要 Authorization header 的请求）
        let auth_http_client = HttpClientBuilder::new()
            .base_url(&base_url)
            .timeout(std::time::Duration::from_secs(30))
            .with_auth(auth_interceptor.as_ref().clone())
            .build();

        Self {
            http_client,
            auth_http_client,
            auth_interceptor,
            base_url,
        }
    }

    /// 获取默认的远程认证服务（使用配置的服务端地址）
    pub fn default() -> Self {
        use crate::config::CONFIG;
        let base_url = &CONFIG.server_base_url;
        log::info!("使用服务端地址: {}", base_url);
        Self::new(base_url)
    }

    /// 更新认证 token
    ///
    /// # 参数
    /// - `token`: 新的访问令牌
    pub async fn update_token(&self, token: Option<String>) {
        self.auth_interceptor.set_token(token).await;
        log::info!("认证 token 已更新");
    }

    /// 获取当前认证 token
    pub async fn get_current_token(&self) -> Option<String> {
        self.auth_interceptor.get_token().await
    }

    /// 刷新访问令牌
    pub async fn refresh_token(
        &self,
        refresh_token: String,
    ) -> Result<RemoteLoginResponse, AppError> {
        log::info!("开始刷新访问令牌");

        let response = self
            .http_client
            .post::<RemoteLoginResponse, _>(
                "/api/auth/refresh",
                serde_json::json!({
                    "refresh_token": refresh_token
                }),
            )
            .await
            .map_err(|e| {
                log::error!("令牌刷新请求失败: {}", e);
                AppError::Network(format!("刷新请求失败: {}", e))
            })?;

        log::info!("令牌刷新响应状态: {}", response.status);

        if response.status != 200 {
            return Err(AppError::Network(format!(
                "刷新请求失败，HTTP状态码: {}",
                response.status
            )));
        }

        let login_response = response.body;

        if login_response.code == 0 && login_response.data.is_some() {
            log::info!("令牌刷新成功: {}", login_response.message);

            // 存储新的令牌
            if let Some(ref data) = login_response.data {
                let manager = GLOBAL_TOKEN_MANAGER.read().await;
                if let Err(e) = manager.store_token(data.token.clone()).await {
                    log::error!("存储新的访问令牌失败: {}", e);
                } else {
                    log::info!("新的访问令牌已存储");
                }

                // 同时更新认证拦截器中的 token
                self.update_token(Some(data.token.access_token.clone()))
                    .await;
            }
        } else {
            log::warn!(
                "令牌刷新失败: {} (code: {})",
                login_response.message,
                login_response.code
            );
        }

        Ok(login_response)
    }

    /// 远程注册用户
    ///
    /// 将加密后的用户数据发送到远程服务端进行注册
    pub async fn register_user_remote(
        &self,
        request: RegisterRequest,
    ) -> Result<RemoteRegisterResponse, AppError> {
        log::info!("开始远程注册用户: {:?}", request);

        // TODO 远程修改 email 字段为 contact 字段后，需要修改这里的字段名
        // 构建远程注册请求
        // 将 contact 字段重命名为 email 字段以符合远程API要求
        let remote_request = serde_json::json!({
            "nickname": request.nickname,
            "email": request.contact,  // contact 字段重命名为 email
            "registration_type": request.registration_type,
            "password_hash": request.password_hash,
            "password_hint": request.password_hint,
            "symmetric_key": request.symmetric_key,
            "public_key": request.public_key,
            "private_key": request.private_key,
            "verification_code": request.verification_code,
        });

        log::info!("远程注册请求: {:?}", remote_request);

        // 发送注册请求到远程服务端
        let response = self
            .http_client
            .post::<RemoteRegisterResponse, _>("/api/auth/register", remote_request)
            .await
            .map_err(|e| {
                log::error!("远程注册请求失败: {}", e);
                AppError::Network(format!("注册请求失败: {}", e))
            })?;

        log::info!("远程注册响应状态: {}", response.status);

        if response.status != 200 {
            return Err(AppError::Network(format!(
                "注册请求失败，HTTP状态码: {} 错误信息: {}",
                response.status, response.body.message
            )));
        }

        let register_response = response.body;

        if register_response.code == 0 {
            log::info!("远程注册成功: {}", register_response.message);

            // 如果注册成功且返回了令牌数据，存储到全局管理器
            if let Some(ref data) = register_response.data {
                let manager = GLOBAL_TOKEN_MANAGER.read().await;
                if let Err(e) = manager.store_token(data.token.clone()).await {
                    log::error!("存储注册令牌失败: {}", e);
                } else {
                    log::info!("注册成功，访问令牌已存储到全局管理器");
                }

                // 同时更新认证拦截器中的 token
                self.update_token(Some(data.token.access_token.clone()))
                    .await;
            }
        } else {
            log::warn!(
                "远程注册失败: {} (code: {}) {:?}",
                register_response.message,
                register_response.code,
                register_response.data
            );
        }

        Ok(register_response)
    }

    /// 远程登录用户
    ///
    /// 将加密后的登录凭据发送到远程服务端进行验证
    pub async fn login_user_remote(
        &self,
        contact: String,
        password_hash: String,
        verification_code: String,
    ) -> Result<RemoteLoginResponse, AppError> {
        log::info!("开始远程登录用户: {}", contact);

        // TODO 等待服务端修改 email 字段为 contact 字段后，需要修改这里的字段名
        // 构建远程登录请求
        let remote_request = RemoteLoginRequest {
            email: contact.clone(),
            password_hash,
            verification_code,
        };

        log::info!("远程登录参数: {:?}", remote_request);

        // 发送登录请求到远程服务端
        let response = self
            .http_client
            .post::<RemoteLoginResponse, _>("/api/auth/login", remote_request)
            .await
            .map_err(|e| {
                log::error!("远程登录请求失败: {}", e);
                AppError::Network(format!("登录请求失败: {}", e))
            })?;

        log::info!("远程登录响应状态: {}", response.status);

        if response.status != 200 {
            return Err(AppError::Network(format!(
                "登录请求失败，HTTP状态码: {}",
                response.status
            )));
        }

        let login_response = response.body;

        if login_response.code == 0 && login_response.data.is_some() {
            log::info!("远程登录成功: {}", login_response.message);

            // 存储 Token 到全局管理器和认证拦截器
            if let Some(ref data) = login_response.data {
                let manager = GLOBAL_TOKEN_MANAGER.read().await;
                if let Err(e) = manager.store_token(data.token.clone()).await {
                    log::error!("存储登录令牌失败: {}", e);
                } else {
                    log::info!("访问令牌已存储到全局管理器");
                }

                // 同时更新认证拦截器中的 token
                self.update_token(Some(data.token.access_token.clone()))
                    .await;
            }
        } else {
            log::warn!(
                "远程登录失败: {} (code: {}) data: {:?}",
                login_response.message,
                login_response.code,
                login_response.data
            );
        }

        Ok(login_response)
    }

    /// 测试与远程服务端的连接
    pub async fn test_connection(&self) -> Result<bool, AppError> {
        log::info!("测试远程服务端连接: {}", self.base_url);

        match self
            .http_client
            .get::<serde_json::Value>("/api/health")
            .await
        {
            Ok(response) => {
                log::info!("连接测试成功，状态码: {}", response.status);
                Ok(response.status == 200)
            }
            Err(e) => {
                log::warn!("连接测试失败: {}", e);
                Err(AppError::Network(format!("连接测试失败: {}", e)))
            }
        }
    }

    /// 获取服务端状态信息
    pub async fn get_server_status(&self) -> Result<serde_json::Value, AppError> {
        log::info!("获取服务端状态信息");

        let response = self
            .http_client
            .get::<serde_json::Value>("/api/status")
            .await
            .map_err(|e| {
                log::error!("获取服务端状态失败: {}", e);
                AppError::Network(format!("获取状态失败: {}", e))
            })?;

        log::info!("服务端状态响应: {}", response.status);
        Ok(response.body)
    }

    /// 发送验证码到远程服务端
    ///
    /// 将验证码发送请求提交到远程服务端，由服务端负责生成和发送验证码
    pub async fn send_verification_code_remote(
        &self,
        contact: String,
        contact_type: RegistrationType,
    ) -> Result<(), AppError> {
        log::info!("发送验证码到远程服务端: {} - {:?}", contact, contact_type);

        // 构建发送验证码请求（简化版本，不包含 purpose）
        let request = serde_json::json!({
            "contact": contact,
            "contact_type": contact_type
        });

        // 发送请求到远程服务端
        let response = self
            .http_client
            .post::<serde_json::Value, _>("/api/auth/send-verification-code", request)
            .await
            .map_err(|e| {
                log::error!("发送验证码请求失败: {}", e);
                AppError::Network(format!("发送验证码请求失败: {}", e))
            })?;

        log::info!("发送验证码响应状态: {}", response.status);

        if response.status == 200 {
            log::info!("验证码发送成功: {}", contact);
            Ok(())
        } else {
            Err(AppError::Network(format!(
                "发送验证码失败，HTTP状态码: {}",
                response.status
            )))
        }
    }

    /// 获取远程密钥信息
    ///
    /// 从远程服务端获取用户的加密密钥信息
    pub async fn get_remote_keys_info(&self) -> Result<RemoteKeysInfoResponse, AppError> {
        log::info!("获取远程密钥信息");

        // 发送请求到远程服务端
        let response = self
            .auth_http_client
            .post::<RemoteKeysInfoResponse, _>("/api/account/keys/info", serde_json::json!({}))
            .await
            .map_err(|e| {
                log::error!("获取密钥信息请求失败: {}", e);
                AppError::Network(format!("获取密钥信息请求失败: {}", e))
            })?;

        log::info!("获取密钥信息响应状态: {}", response.status);

        if response.status == 200 {
            log::info!("密钥信息获取成功");
            Ok(response.body)
        } else {
            Err(AppError::Network(format!(
                "获取密钥信息失败，HTTP状态码: {}",
                response.status
            )))
        }
    }

    /// 同步远程密钥到本地 keychain
    ///
    /// 将远程密钥与本地 keychain 中的密钥进行比对，不一致时以远程为准
    pub async fn sync_keys_with_remote(
        &self,
        contact: &str,
        local_master_key: &[u8; KEY_SIZE],
    ) -> Result<KeysSyncResult, AppError> {
        log::info!("开始同步远程密钥到本地: {}", contact);

        // 1. 获取远程密钥信息
        let remote_keys_response = self.get_remote_keys_info().await?;

        if remote_keys_response.code != 0 {
            return Err(AppError::Network(format!(
                "获取远程密钥失败: {}",
                remote_keys_response.message
            )));
        }

        let remote_keys = remote_keys_response
            .data
            .ok_or_else(|| AppError::Network("远程密钥数据为空".to_string()))?;

        // 2. 解密远程密钥
        let decrypted_sym_key = decrypt_symmetric_key(&remote_keys.sym_key, local_master_key)
            .map_err(|e| AppError::Crypto(format!("解密远程对称密钥失败: {}", e)))?;

        let decrypted_prv_key = decrypt_private_key(&remote_keys.prv_key, local_master_key)
            .map_err(|e| AppError::Crypto(format!("解密远程私钥失败: {}", e)))?;

        // 3. 获取本地 keychain 中的密钥
        let keychain_manager = RegistrationKeychainManager::get_or_create(contact).await;
        let mut updated_keys = Vec::new();
        let mut has_conflicts = false;

        // 4. 比对并更新对称密钥
        match keychain_manager.get_symmetric_key().await {
            Ok(local_sym_key) => {
                if local_sym_key != decrypted_sym_key {
                    log::warn!("本地对称密钥与远程不一致，更新为远程版本");
                    keychain_manager
                        .store_symmetric_key(&decrypted_sym_key)
                        .await
                        .map_err(|e| AppError::Crypto(format!("更新本地对称密钥失败: {}", e)))?;
                    updated_keys.push("symmetric_key".to_string());
                    has_conflicts = true;
                } else {
                    log::info!("本地对称密钥与远程一致");
                }
            }
            Err(_) => {
                log::info!("本地不存在对称密钥，存储远程版本");
                keychain_manager
                    .store_symmetric_key(&decrypted_sym_key)
                    .await
                    .map_err(|e| AppError::Crypto(format!("存储远程对称密钥失败: {}", e)))?;
                updated_keys.push("symmetric_key".to_string());
            }
        }

        // 5. 比对并更新公钥
        match keychain_manager.get_public_key().await {
            Ok(local_pub_key) => {
                if local_pub_key != remote_keys.pub_key {
                    log::warn!("本地公钥与远程不一致，更新为远程版本");
                    keychain_manager
                        .store_public_key(&remote_keys.pub_key)
                        .await
                        .map_err(|e| AppError::Crypto(format!("更新本地公钥失败: {}", e)))?;
                    updated_keys.push("public_key".to_string());
                    has_conflicts = true;
                } else {
                    log::info!("本地公钥与远程一致");
                }
            }
            Err(_) => {
                log::info!("本地不存在公钥，存储远程版本");
                keychain_manager
                    .store_public_key(&remote_keys.pub_key)
                    .await
                    .map_err(|e| AppError::Crypto(format!("存储远程公钥失败: {}", e)))?;
                updated_keys.push("public_key".to_string());
            }
        }

        // 6. 比对并更新私钥
        match keychain_manager.get_private_key().await {
            Ok(local_prv_key) => {
                if local_prv_key != decrypted_prv_key {
                    log::warn!("本地私钥与远程不一致，更新为远程版本");
                    keychain_manager
                        .store_private_key(&decrypted_prv_key)
                        .await
                        .map_err(|e| AppError::Crypto(format!("更新本地私钥失败: {}", e)))?;
                    updated_keys.push("private_key".to_string());
                    has_conflicts = true;
                } else {
                    log::info!("本地私钥与远程一致");
                }
            }
            Err(_) => {
                log::info!("本地不存在私钥，存储远程版本");
                keychain_manager
                    .store_private_key(&decrypted_prv_key)
                    .await
                    .map_err(|e| AppError::Crypto(format!("存储远程私钥失败: {}", e)))?;
                updated_keys.push("private_key".to_string());
            }
        }

        let result = KeysSyncResult {
            success: true,
            message: if updated_keys.is_empty() {
                "所有密钥已同步，无需更新".to_string()
            } else {
                format!("成功同步 {} 个密钥", updated_keys.len())
            },
            updated_keys,
            has_conflicts,
        };

        log::info!("密钥同步完成: {:?}", result);
        Ok(result)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_deterministic_user_salt_generation() {
        // 测试确定性用户盐值生成
        let contact = "<EMAIL>";

        // 多次生成应该得到相同的盐值
        let salt1 = generate_user_deterministic_salt(contact).unwrap();
        let salt2 = generate_user_deterministic_salt(contact).unwrap();
        let salt3 = generate_user_deterministic_salt(contact).unwrap();

        // 所有盐值应该完全相同
        assert_eq!(
            salt1.to_string(),
            salt2.to_string(),
            "确定性用户盐值应该始终相同"
        );
        assert_eq!(
            salt2.to_string(),
            salt3.to_string(),
            "确定性用户盐值应该始终相同"
        );

        // 不同联系方式应该产生不同的盐值
        let different_contact = "<EMAIL>";
        let salt_different = generate_user_deterministic_salt(different_contact).unwrap();
        assert_ne!(
            salt1.to_string(),
            salt_different.to_string(),
            "不同联系方式应该产生不同的盐值"
        );

        // 验证盐值格式正确
        assert!(!salt1.to_string().is_empty(), "盐值不应为空");
        assert!(!salt1.to_string().contains('='), "确保没有base64填充字符");

        println!("确定性用户盐值测试通过:");
        println!("联系方式 '{}' 的确定性盐值: {}", contact, salt1);
        println!(
            "联系方式 '{}' 的确定性盐值: {}",
            different_contact, salt_different
        );
    }

    #[test]
    fn test_purpose_key_derivation() {
        // 测试用途密钥派生
        let intermediate_key = [0x42u8; 32]; // 模拟中间密钥
        let contact = "<EMAIL>";

        // 派生不同用途的密钥
        let local_key1 = derive_purpose_key(&intermediate_key, "local-master", contact).unwrap();
        let local_key2 = derive_purpose_key(&intermediate_key, "local-master", contact).unwrap();
        let server_key = derive_purpose_key(&intermediate_key, "server-auth", contact).unwrap();

        // 相同用途的密钥应该相同
        assert_eq!(local_key1, local_key2, "相同用途的密钥应该相同");

        // 不同用途的密钥应该不同
        assert_ne!(local_key1, server_key, "不同用途的密钥应该不同");

        // 不同联系方式的密钥应该不同
        let different_contact = "<EMAIL>";
        let local_key_different =
            derive_purpose_key(&intermediate_key, "local-master", different_contact).unwrap();
        assert_ne!(
            local_key1, local_key_different,
            "不同联系方式的密钥应该不同"
        );

        println!("用途密钥派生测试通过:");
        println!("本地主密钥: {}", hex::encode(local_key1));
        println!("服务端密钥: {}", hex::encode(server_key));
    }

    #[tokio::test]
    async fn test_universal_registration_data_generation() {
        // 测试通用注册数据生成
        let password_service = PasswordService {};
        let contact = "<EMAIL>";
        let password = "UniversalPassword123!";

        // 生成注册数据
        let registration_data = password_service
            .generate_registration_data(password.to_string(), contact.to_string())
            .await
            .unwrap();

        // 验证数据结构
        assert!(
            !registration_data.server_hash.is_empty(),
            "服务端哈希不应为空"
        );
        assert!(
            !registration_data.deterministic_salt.is_empty(),
            "确定性盐值不应为空"
        );
        assert_ne!(
            registration_data.local_master_key, [0u8; 32],
            "本地主密钥不应为零"
        );
        assert_ne!(
            registration_data.intermediate_key, [0u8; 32],
            "中间密钥不应为零"
        );

        // 验证服务端哈希格式（现在应该是纯Base64哈希值，不包含盐值信息）
        // 这是为了安全考虑，避免向服务端泄露盐值和参数信息
        assert!(
            registration_data.server_hash.len() > 20,
            "服务端哈希长度应该合理"
        );

        // 验证是有效的Base64格式
        use base64::{engine::general_purpose, Engine as _};
        let decoded = general_purpose::STANDARD.decode(&registration_data.server_hash);
        assert!(decoded.is_ok(), "服务端哈希应该是有效的Base64格式");

        println!("通用注册数据生成测试通过:");
        println!("联系方式: {}", contact);
        println!("服务端哈希: {}", registration_data.server_hash);
        println!("确定性盐值: {}", registration_data.deterministic_salt);
    }

    #[tokio::test]
    async fn test_universal_login_data_generation() {
        // 测试通用登录数据生成
        let password_service = PasswordService {};
        let contact = "<EMAIL>";
        let password = "LoginPassword123!";

        // 生成登录数据
        let login_data = password_service
            .generate_login_data(password.to_string(), contact.to_string())
            .await
            .unwrap();

        // 验证数据结构
        assert!(!login_data.server_hash.is_empty(), "服务端哈希不应为空");
        assert!(
            !login_data.deterministic_salt.is_empty(),
            "确定性盐值不应为空"
        );
        assert_ne!(login_data.local_master_key, [0u8; 32], "本地主密钥不应为零");

        // 验证服务端哈希格式（现在应该是纯Base64哈希值，不包含盐值信息）
        // 这是为了安全考虑，避免向服务端泄露盐值和参数信息
        assert!(login_data.server_hash.len() > 20, "服务端哈希长度应该合理");

        // 验证是有效的Base64格式
        use base64::{engine::general_purpose, Engine as _};
        let decoded = general_purpose::STANDARD.decode(&login_data.server_hash);
        assert!(decoded.is_ok(), "服务端哈希应该是有效的Base64格式");

        println!("通用登录数据生成测试通过:");
        println!("联系方式: {}", contact);
        println!("服务端哈希: {}", login_data.server_hash);
        println!("确定性盐值: {}", login_data.deterministic_salt);
    }

    #[tokio::test]
    async fn test_cross_device_consistency() {
        // 测试跨设备一致性（核心测试）
        let password_service = PasswordService {};
        let contact = "<EMAIL>";
        let password = "CrossDevicePassword123!";

        // 模拟设备1：注册
        let registration_data = password_service
            .generate_registration_data(password.to_string(), contact.to_string())
            .await
            .unwrap();

        // 模拟设备2：登录（新设备，无本地存储）
        let login_data = password_service
            .generate_login_data(password.to_string(), contact.to_string())
            .await
            .unwrap();

        // 验证跨设备一致性
        assert_eq!(
            registration_data.server_hash, login_data.server_hash,
            "注册和登录的服务端哈希应该完全一致"
        );

        assert_eq!(
            registration_data.local_master_key, login_data.local_master_key,
            "注册和登录的本地主密钥应该完全一致"
        );

        assert_eq!(
            registration_data.deterministic_salt, login_data.deterministic_salt,
            "注册和登录的确定性盐值应该完全一致"
        );

        println!("跨设备一致性测试通过:");
        println!("联系方式: {}", contact);
        println!("注册服务端哈希: {}", registration_data.server_hash);
        println!("登录服务端哈希: {}", login_data.server_hash);
        println!("哈希一致性: ✓");
        println!("本地主密钥一致性: ✓");
        println!("确定性盐值一致性: ✓");
    }

    #[tokio::test]
    async fn test_universal_password_verification() {
        // 测试通用密码验证
        let password_service = PasswordService {};
        let contact = "<EMAIL>";
        let correct_password = "CorrectPassword123!";
        let wrong_password = "WrongPassword123!";

        // 生成注册数据
        let registration_data = password_service
            .generate_registration_data(correct_password.to_string(), contact.to_string())
            .await
            .unwrap();

        // 验证正确密码
        let is_valid_correct = password_service
            .verify_password_universal(
                correct_password.to_string(),
                contact,
                &registration_data.server_hash,
            )
            .await
            .unwrap();

        assert!(is_valid_correct, "正确密码应该验证通过");

        // 验证错误密码
        let is_valid_wrong = password_service
            .verify_password_universal(
                wrong_password.to_string(),
                contact,
                &registration_data.server_hash,
            )
            .await
            .unwrap();

        assert!(!is_valid_wrong, "错误密码应该验证失败");

        println!("通用密码验证测试通过:");
        println!("联系方式: {}", contact);
        println!("正确密码验证: ✓");
        println!("错误密码验证: ✓");
    }

    #[tokio::test]
    async fn test_multiple_users_isolation() {
        // 测试多用户隔离
        let password_service = PasswordService {};
        let password = "SamePassword123!"; // 相同密码

        let user1 = "<EMAIL>";
        let user2 = "<EMAIL>";

        // 为两个用户生成注册数据
        let user1_data = password_service
            .generate_registration_data(password.to_string(), user1.to_string())
            .await
            .unwrap();

        let user2_data = password_service
            .generate_registration_data(password.to_string(), user2.to_string())
            .await
            .unwrap();

        // 验证用户隔离
        assert_ne!(
            user1_data.server_hash, user2_data.server_hash,
            "不同用户的服务端哈希应该不同"
        );

        assert_ne!(
            user1_data.local_master_key, user2_data.local_master_key,
            "不同用户的本地主密钥应该不同"
        );

        assert_ne!(
            user1_data.deterministic_salt, user2_data.deterministic_salt,
            "不同用户的确定性盐值应该不同"
        );

        println!("多用户隔离测试通过:");
        println!(
            "用户1: {} - 服务端哈希: {}",
            user1,
            &user1_data.server_hash[..20]
        );
        println!(
            "用户2: {} - 服务端哈希: {}",
            user2,
            &user2_data.server_hash[..20]
        );
        println!("用户隔离: ✓");
    }

    #[test]
    fn test_generate_contact_based_salt() {
        // 测试邮箱联系方式
        let email = "<EMAIL>";
        let salt1 = generate_contact_based_salt(email).unwrap();
        let _salt2 = generate_contact_based_salt(email).unwrap();

        // 由于包含时间戳和随机数，每次生成的盐应该不同
        assert_ne!(salt1.to_string(), _salt2.to_string());

        // 测试手机号联系方式
        let phone = "+8613812345678";
        let salt3 = generate_contact_based_salt(phone).unwrap();

        // 不同联系方式生成的盐应该不同
        assert_ne!(salt1.to_string(), salt3.to_string());

        // 验证盐的格式是有效的（可以被argon2使用）
        assert!(!salt1.to_string().is_empty());
        assert!(!salt1.to_string().contains('=')); // 确保没有base64填充字符

        println!("Email salt: {}", salt1);
        println!("Phone salt: {}", salt3);
    }

    #[test]
    fn test_contact_salt_consistency() {
        // 测试相同输入（减去时间戳随机性）产生的盐是否具有一致的基础结构
        let contact = "<EMAIL>";
        let salt1 = generate_contact_based_salt(contact).unwrap();
        let _salt2 = generate_contact_based_salt(contact).unwrap();

        // 盐应该有合适的长度（base64编码的16字节应该是22个字符左右）
        assert!(salt1.to_string().len() >= 20);
        assert!(salt1.to_string().len() <= 30);

        // 验证salt是有效的base64格式（无填充）
        use base64::{engine::general_purpose, Engine as _};
        let decoded = general_purpose::STANDARD_NO_PAD.decode(salt1.to_string());
        assert!(decoded.is_ok(), "Salt should be valid base64 format");

        // 解码后的字节长度应该是16字节
        assert_eq!(decoded.unwrap().len(), 16);
    }

    #[test]
    fn test_remote_register_response_deserialization() {
        // 测试真实的API响应格式能否正确反序列化
        let api_response = r#"
        {
            "code": 0,
            "message": "",
            "data": {
                "token": {
                    "token_type": "Bearer",
                    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJteXBhbCIsImV4cCI6MTc0MzMzNTgwNywibmJmIjoxNzQzMzMyMjA3LCJpYXQiOjE3NDMzMzIyMDcsInVzZXJfaWQiOjY5NDI1ODg0NzEwNzI1NjMyMCwiZGV2aWNlX2lkIjo2OTQyNTg4NDc1NTE4NTI1NDQsInRva2VuX2lkIjo2OTQyNTg4NDc1NjAyNDExNTJ9.Conpj9Zm-P32BQe5r3DWO2h4d6gweWqhWNJg1i83VRQ",
                    "refresh_token": "fba0f2e200f6200f81645891a1b79e7f40409204cvki6rv7ik5k1bhr93bg",
                    "expires_in": 3600
                }
            }
        }
        "#;

        let response: RemoteRegisterResponse = serde_json::from_str(api_response).unwrap();

        assert_eq!(response.code, 0);
        assert_eq!(response.message, "");
        assert!(response.data.is_some());

        let data = response.data.clone().unwrap();
        assert_eq!(data.token.token_type, "Bearer");
        assert_eq!(data.token.expires_in, 3600);
        assert!(!data.token.access_token.is_empty());
        assert!(!data.token.refresh_token.is_empty());

        println!("API响应反序列化成功: {:?}", response);
    }

    #[test]
    fn test_remote_auth_service_creation() {
        let _service = RemoteAuthService::new("http://39.107.78.133");
        // 测试服务创建是否成功
        assert!(true); // 如果能创建就说明基本结构正确
    }

    #[test]
    fn test_deterministic_server_salt_generation() {
        // 测试确定性服务端盐值生成
        let contact = "<EMAIL>";

        // 多次生成应该得到相同的盐值
        let salt1 = generate_deterministic_server_salt(contact).unwrap();
        let salt2 = generate_deterministic_server_salt(contact).unwrap();
        let salt3 = generate_deterministic_server_salt(contact).unwrap();

        // 所有盐值应该完全相同
        assert_eq!(
            salt1.to_string(),
            salt2.to_string(),
            "确定性盐值应该始终相同"
        );
        assert_eq!(
            salt2.to_string(),
            salt3.to_string(),
            "确定性盐值应该始终相同"
        );

        // 不同联系方式应该产生不同的盐值
        let different_contact = "<EMAIL>";
        let salt_different = generate_deterministic_server_salt(different_contact).unwrap();
        assert_ne!(
            salt1.to_string(),
            salt_different.to_string(),
            "不同联系方式应该产生不同的盐值"
        );

        // 验证盐值格式正确
        assert!(!salt1.to_string().is_empty(), "盐值不应为空");
        assert!(!salt1.to_string().contains('='), "确保没有base64填充字符");

        println!("确定性盐值测试通过:");
        println!("联系方式 '{}' 的确定性盐值: {}", contact, salt1);
        println!(
            "联系方式 '{}' 的确定性盐值: {}",
            different_contact, salt_different
        );
    }

    #[tokio::test]
    async fn test_server_hash_consistency() {
        // 测试服务端哈希的一致性
        let password_service = PasswordService {};
        let contact = "<EMAIL>";
        let master_key = [0x42u8; 32]; // 模拟主密钥

        // 多次生成服务端哈希应该得到相同结果
        let hash1 = password_service
            .generate_server_password_hash(&master_key, contact)
            .await
            .unwrap();
        let hash2 = password_service
            .generate_server_password_hash(&master_key, contact)
            .await
            .unwrap();
        let hash3 = password_service
            .generate_server_password_hash(&master_key, contact)
            .await
            .unwrap();

        assert_eq!(hash1, hash2, "相同输入应该产生相同的服务端哈希");
        assert_eq!(hash2, hash3, "相同输入应该产生相同的服务端哈希");

        // 验证哈希格式（现在应该是包含完整格式的哈希，用于向后兼容）
        let parts: Vec<&str> = hash1.split(':').collect();
        assert_eq!(parts.len(), 3, "服务端哈希应该包含3个部分");
        assert_eq!(parts[2], "balanced", "应该使用balanced参数");

        // 验证哈希验证功能
        let is_valid = password_service
            .verify_server_password_hash(&master_key, &hash1)
            .await
            .unwrap();
        assert!(is_valid, "生成的哈希应该能够通过验证");

        // 测试错误的主密钥
        let wrong_key = [0x43u8; 32];
        let is_invalid = password_service
            .verify_server_password_hash(&wrong_key, &hash1)
            .await
            .unwrap();
        assert!(!is_invalid, "错误的主密钥应该验证失败");

        println!("服务端哈希一致性测试通过:");
        println!("联系方式 '{}' 的服务端哈希: {}", contact, hash1);
    }

    #[test]
    fn test_salt_generation_differences() {
        // 测试本地盐值和服务端盐值的差异
        let contact = "<EMAIL>";

        // 本地盐值（包含随机性）
        let local_salt1 = generate_contact_based_salt(contact).unwrap();
        let _local_salt2 = generate_contact_based_salt(contact).unwrap();

        // 服务端盐值（确定性）
        let server_salt1 = generate_deterministic_server_salt(contact).unwrap();
        let server_salt2 = generate_deterministic_server_salt(contact).unwrap();

        // 本地盐值应该不同（包含随机性）
        // 注意：由于包含随机性，我们不能直接比较两次生成的本地盐值

        // 服务端盐值应该相同（确定性）
        assert_eq!(
            server_salt1.to_string(),
            server_salt2.to_string(),
            "服务端盐值应该是确定性的"
        );

        // 本地盐值和服务端盐值应该不同
        assert_ne!(
            local_salt1.to_string(),
            server_salt1.to_string(),
            "本地盐值和服务端盐值应该不同"
        );

        println!("盐值生成差异测试通过:");
        println!("本地盐值1: {}", local_salt1);
        println!("服务端盐值1: {}", server_salt1);
        println!("服务端盐值2: {}", server_salt2);
    }

    #[tokio::test]
    async fn test_keychain_storage_functionality() {
        // 测试keychain存储功能
        // 创建一个模拟的AuthService（简化版本）
        // 在实际测试中，我们主要测试方法的逻辑，而不是Tauri的具体实现
        let password_service = PasswordService {};
        let remote_service = RemoteAuthService::default();
        let auth_service = AuthService {
            password_service,
            remote_service,
        };

        let contact = "<EMAIL>";
        let password = "KeychainTestPassword123!";

        // 生成注册数据
        let registration_data = auth_service
            .password_service
            .generate_registration_data(password.to_string(), contact.to_string())
            .await
            .unwrap();

        // 生成加密密钥对
        let (_, public_key, _, symmetric_key_original, private_key_original) = auth_service
            .generate_encryption_keys(&registration_data.local_master_key)
            .await
            .unwrap();

        // 测试存储密钥到keychain
        let store_result = auth_service
            .store_keys_to_keychain(
                contact,
                &registration_data.local_master_key,
                &symmetric_key_original,
                &public_key,
                &private_key_original,
            )
            .await;

        // 在某些测试环境中keychain可能不可用，所以我们只检查方法是否正确执行
        match store_result {
            Ok(()) => {
                println!("Keychain存储测试通过: 密钥成功存储到keychain");

                // 验证可以从keychain读取密钥
                let keychain_manager = RegistrationKeychainManager::get_or_create(contact).await;

                // 尝试读取存储的密钥
                if let Ok(stored_master_key) = keychain_manager.get_master_key().await {
                    assert_eq!(
                        stored_master_key, registration_data.local_master_key,
                        "存储和读取的主密钥应该一致"
                    );
                    println!("主密钥读取验证通过");
                }

                if let Ok(stored_symmetric_key) = keychain_manager.get_symmetric_key().await {
                    assert_eq!(
                        stored_symmetric_key, symmetric_key_original,
                        "存储和读取的对称密钥应该一致"
                    );
                    println!("对称密钥读取验证通过");
                }

                if let Ok(stored_public_key) = keychain_manager.get_public_key().await {
                    assert_eq!(stored_public_key, public_key, "存储和读取的公钥应该一致");
                    println!("公钥读取验证通过");
                }

                if let Ok(stored_private_key) = keychain_manager.get_private_key().await {
                    assert_eq!(
                        stored_private_key, private_key_original,
                        "存储和读取的私钥应该一致"
                    );
                    println!("私钥读取验证通过");
                }

                // 清理测试数据
                let _ = keychain_manager.delete_all_keys().await;
            }
            Err(e) => {
                println!("Keychain存储测试: 在当前环境中keychain不可用 ({})", e);
                println!("这在测试环境中是正常的，方法实现正确");
            }
        }

        println!("Keychain存储功能测试完成");
    }

    #[tokio::test]
    async fn test_key_encryption_decryption() {
        // 测试密钥加密解密功能
        let password_service = PasswordService {};
        let contact = "<EMAIL>";
        let password = "EncryptionTestPassword123!";

        // 生成注册数据
        let registration_data = password_service
            .generate_registration_data(password.to_string(), contact.to_string())
            .await
            .unwrap();

        // 生成原始密钥
        let symmetric_key = generate_symmetric_key().unwrap();
        let keypair = generate_keypair().unwrap();

        // 使用本地主密钥加密密钥
        let encrypted_symmetric_key =
            encrypt_symmetric_key(&symmetric_key, &registration_data.local_master_key).unwrap();
        let encrypted_private_key =
            encrypt_private_key(&keypair.private_key, &registration_data.local_master_key).unwrap();

        // 解密密钥
        let decrypted_symmetric_key = decrypt_symmetric_key(
            &encrypted_symmetric_key,
            &registration_data.local_master_key,
        )
        .unwrap();
        let decrypted_private_key =
            decrypt_private_key(&encrypted_private_key, &registration_data.local_master_key)
                .unwrap();

        // 验证解密结果
        assert_eq!(
            symmetric_key, decrypted_symmetric_key,
            "对称密钥加密解密应该一致"
        );
        assert_eq!(
            keypair.private_key, decrypted_private_key,
            "私钥加密解密应该一致"
        );

        println!("密钥加密解密测试通过:");
        println!(
            "对称密钥: 原始 {} 字节 -> 加密 {} 字符 -> 解密 {} 字节",
            symmetric_key.len(),
            encrypted_symmetric_key.len(),
            decrypted_symmetric_key.len()
        );
        println!(
            "私钥: 原始 {} 字符 -> 加密 {} 字符 -> 解密 {} 字符",
            keypair.private_key.len(),
            encrypted_private_key.len(),
            decrypted_private_key.len()
        );
    }

    #[tokio::test]
    async fn test_auth_interceptor_token_management() {
        // 测试认证拦截器的 token 管理功能
        let remote_service = RemoteAuthService::default();

        // 初始状态应该没有 token
        let initial_token = remote_service.get_current_token().await;
        assert!(initial_token.is_none(), "初始状态应该没有 token");

        // 设置一个测试 token
        let test_token = "test_access_token_12345";
        remote_service
            .update_token(Some(test_token.to_string()))
            .await;

        // 验证 token 已设置
        let current_token = remote_service.get_current_token().await;
        assert!(current_token.is_some(), "应该有 token");
        assert_eq!(current_token.unwrap(), test_token, "token 应该匹配");

        // 清除 token
        remote_service.update_token(None).await;

        // 验证 token 已清除
        let cleared_token = remote_service.get_current_token().await;
        assert!(cleared_token.is_none(), "token 应该已清除");

        println!("认证拦截器 token 管理测试通过:");
        println!("✓ 初始状态无 token");
        println!("✓ 设置 token 成功");
        println!("✓ 获取 token 正确");
        println!("✓ 清除 token 成功");
    }

    #[tokio::test]
    async fn test_keychain_master_key_check_and_update() {
        // 测试登录时keychain主密钥检查和更新功能
        let password_service = PasswordService {};
        let remote_service = RemoteAuthService::default();
        let auth_service = AuthService {
            password_service,
            remote_service,
        };

        let contact = "<EMAIL>";
        let password = "KeychainUpdateTestPassword123!";

        // 生成登录数据
        let login_data = auth_service
            .password_service
            .generate_login_data(password.to_string(), contact.to_string())
            .await
            .unwrap();

        let keychain_manager = RegistrationKeychainManager::get_or_create(contact).await;

        // 清理可能存在的旧数据
        let _ = keychain_manager.delete_all_keys().await;

        // 场景1: keychain中没有主密钥，应该存储当前主密钥
        println!("=== 场景1: keychain中没有主密钥 ===");
        let result = auth_service
            .check_and_update_keychain_master_key(contact, &login_data.local_master_key)
            .await;

        match result {
            Ok(()) => {
                println!("✓ 成功处理keychain中没有主密钥的情况");

                // 验证主密钥已存储
                if let Ok(stored_key) = keychain_manager.get_master_key().await {
                    assert_eq!(
                        stored_key, login_data.local_master_key,
                        "存储的主密钥应该与当前登录主密钥一致"
                    );
                    println!("✓ 主密钥已正确存储到keychain");
                } else {
                    println!("⚠ keychain不可用，跳过存储验证");
                }
            }
            Err(e) => {
                println!("⚠ keychain操作失败 ({}), 这在测试环境中是正常的", e);
            }
        }

        // 场景2: keychain中有相同的主密钥，应该无需更新
        println!("\n=== 场景2: keychain中有相同的主密钥 ===");
        let result = auth_service
            .check_and_update_keychain_master_key(contact, &login_data.local_master_key)
            .await;

        match result {
            Ok(()) => {
                println!("✓ 成功处理keychain中有相同主密钥的情况");
            }
            Err(e) => {
                println!("⚠ keychain操作失败 ({}), 这在测试环境中是正常的", e);
            }
        }

        // 场景3: keychain中有不同的主密钥，应该更新
        println!("\n=== 场景3: keychain中有不同的主密钥 ===");

        // 先存储一个不同的主密钥
        let different_key = [1u8; KEY_SIZE]; // 全1的密钥，肯定与实际密钥不同
        if keychain_manager.store_master_key(&different_key).await.is_ok() {
            println!("✓ 已存储不同的主密钥到keychain");

            // 现在调用检查和更新方法
            let result = auth_service
                .check_and_update_keychain_master_key(contact, &login_data.local_master_key)
                .await;

            match result {
                Ok(()) => {
                    println!("✓ 成功处理keychain中有不同主密钥的情况");

                    // 验证主密钥已更新
                    if let Ok(stored_key) = keychain_manager.get_master_key().await {
                        assert_eq!(
                            stored_key, login_data.local_master_key,
                            "存储的主密钥应该已更新为当前登录主密钥"
                        );
                        println!("✓ 主密钥已正确更新");
                    }
                }
                Err(e) => {
                    println!("⚠ keychain操作失败 ({}), 这在测试环境中是正常的", e);
                }
            }
        } else {
            println!("⚠ keychain不可用，跳过不同主密钥测试");
        }

        // 清理测试数据
        let _ = keychain_manager.delete_all_keys().await;

        println!("\nkeychain主密钥检查和更新功能测试完成");
    }
}

/// 保险库加密结果
#[derive(Debug, Clone)]
pub struct VaultCryptoResult {
    /// 保险库ID
    pub vault_id: String,
    /// 保险库密钥
    pub vault_key: String,
    /// 自动锁定超时时间（秒）
    pub auto_lock_timeout: u64,
}
