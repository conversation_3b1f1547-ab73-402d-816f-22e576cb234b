// 系统密钥链集成模块
// 实现跨平台的系统密钥链访问功能

use crate::errors::{VaultError, VaultResult};
use base64::{engine::general_purpose::STANDARD as BASE64_STANDARD, Engine as _};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use std::collections::HashMap;
use thiserror::Error;
use tokio::sync::RwLock;
use zeroize::Zeroize;
use once_cell::sync::Lazy;

// 移动平台特定导入
#[cfg(any(target_os = "android", target_os = "ios"))]
use crate::mobile::{KeychainSecureStorage, KeychainSecureStorageFactory, SecureStorageProvider, MobileError};
#[cfg(any(target_os = "android", target_os = "ios"))]
use once_cell::sync::Lazy as OnceLazy;

// Stronghold 相关导入
use crate::crypto::stronghold::{
    adapter::UnifiedStorageAdapter,
    config::{BackendType, FailoverStrategy},
};

use super::KEY_SIZE;

/// 全局 RegistrationKeychainManager 缓存
static GLOBAL_MANAGER_CACHE: Lazy<Arc<RwLock<HashMap<String, Arc<RegistrationKeychainManager>>>>> = 
    Lazy::new(|| Arc::new(RwLock::new(HashMap::new())));

/// 全局适配器缓存清理任务是否已启动的标志
static CACHE_CLEANUP_STARTED: Lazy<Arc<RwLock<bool>>> = 
    Lazy::new(|| Arc::new(RwLock::new(false)));

/// 密钥链错误类型
#[derive(Error, Debug)]
pub enum KeychainError {
    #[error("密钥链服务不可用")]
    ServiceUnavailable,
    #[error("访问被拒绝")]
    AccessDenied,
    #[error("密钥未找到")]
    KeyNotFound,
    #[error("密钥已存在")]
    KeyExists,
    #[error("无效的服务名称或用户名")]
    InvalidCredentials,
    #[error("系统错误: {0}")]
    SystemError(String),
    #[error("编码错误: {0}")]
    EncodingError(String),
}

impl From<KeychainError> for VaultError {
    fn from(err: KeychainError) -> Self {
        VaultError::InternalError(format!("密钥链错误: {}", err))
    }
}

// 移动平台错误转换
#[cfg(any(target_os = "android", target_os = "ios"))]
impl From<MobileError> for KeychainError {
    fn from(err: MobileError) -> Self {
        match err {
            MobileError::UnsupportedPlatform { .. } => KeychainError::ServiceUnavailable,
            MobileError::SecureStorageError { reason } => KeychainError::SystemError(reason),
            MobileError::PermissionError { permission: _ } => KeychainError::AccessDenied,
            MobileError::InitializationError { reason } => KeychainError::SystemError(reason),
            MobileError::ConfigurationError { reason } => KeychainError::SystemError(reason),
            MobileError::InternalError { reason } => KeychainError::SystemError(reason),
            _ => KeychainError::SystemError(err.to_string()),
        }
    }
}

/// 密钥链条目元数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KeychainEntry {
    /// 服务名称
    pub service: String,
    /// 用户名/账户名
    pub account: String,
    /// 创建时间戳
    pub created_at: i64,
    /// 最后访问时间戳
    pub last_accessed: i64,
    /// 密钥类型
    pub key_type: KeyType,
}

/// 密钥类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum KeyType {
    /// 主密钥
    MasterKey,
    /// 派生密钥
    DerivedKey,
    /// 加密密钥
    EncryptionKey,
    /// 用户定义
    Custom(String),
}

impl KeyType {
    pub fn description(&self) -> &str {
        match self {
            Self::MasterKey => "主密钥",
            Self::DerivedKey => "派生密钥",
            Self::EncryptionKey => "加密密钥",
            Self::Custom(desc) => desc,
        }
    }
}

// 移动平台全局安全存储实例
#[cfg(any(target_os = "android", target_os = "ios"))]
static MOBILE_SECURE_STORAGE: OnceLazy<Arc<RwLock<KeychainSecureStorage>>> = OnceLazy::new(|| {
    log::info!("初始化移动平台全局安全存储实例");
    let storage = KeychainSecureStorageFactory::create_default();
    Arc::new(RwLock::new(storage))
});

/// 跨平台密钥链管理器
#[derive(Debug)]
pub struct KeychainManager {
    service: String,
    account: String,
}

impl KeychainManager {
    /// 创建新的密钥链管理器
    pub fn new(service: &str, account: &str) -> VaultResult<Self> {
        if service.is_empty() || account.is_empty() {
            return Err(VaultError::InvalidInput(
                "服务名称和账户名不能为空".to_string(),
            ));
        }

        Ok(Self {
            service: service.to_string(),
            account: account.to_string(),
        })
    }

    /// 存储密钥到系统密钥链
    pub fn store_key(&self, key: &[u8; KEY_SIZE]) -> VaultResult<()> {
        self.store_key_with_type(key, KeyType::MasterKey)
    }

    /// 存储指定类型的密钥到系统密钥链
    pub fn store_key_with_type(&self, key: &[u8; KEY_SIZE], key_type: KeyType) -> VaultResult<()> {
        let encoded_key = BASE64_STANDARD.encode(key);

        // 创建元数据
        let metadata = KeychainEntry {
            service: self.service.clone(),
            account: self.account.clone(),
            created_at: current_timestamp(),
            last_accessed: current_timestamp(),
            key_type,
        };

        // 将元数据和密钥一起存储
        let entry_data = KeychainEntryData {
            metadata,
            key: encoded_key,
        };

        let serialized = serde_json::to_string(&entry_data)
            .map_err(|e| VaultError::InternalError(format!("序列化失败: {}", e)))?;

        self.store_password(&serialized)
    }

    /// 从系统密钥链获取密钥
    pub fn get_key(&self) -> VaultResult<[u8; KEY_SIZE]> {
        let serialized = self.get_password()?;

        let entry_data: KeychainEntryData = serde_json::from_str(&serialized)
            .map_err(|e| VaultError::InternalError(format!("反序列化失败: {}", e)))?;

        let key_bytes = BASE64_STANDARD
            .decode(&entry_data.key)
            .map_err(|e| VaultError::Base64Decode(e))?;

        if key_bytes.len() != KEY_SIZE {
            return Err(VaultError::InternalError(format!(
                "无效的密钥长度: 期望{}字节，实际{}字节",
                KEY_SIZE,
                key_bytes.len()
            )));
        }

        let mut key = [0u8; KEY_SIZE];
        key.copy_from_slice(&key_bytes);

        // 更新最后访问时间（可选）
        // self.update_last_accessed()?;

        Ok(key)
    }

    /// 删除密钥
    pub fn delete_key(&self) -> VaultResult<()> {
        self.delete_password()
    }

    /// 检查密钥是否存在
    pub fn key_exists(&self) -> bool {
        self.get_password().is_ok()
    }

    /// 获取密钥元数据
    pub fn get_metadata(&self) -> VaultResult<KeychainEntry> {
        let serialized = self.get_password()?;

        let entry_data: KeychainEntryData = serde_json::from_str(&serialized)
            .map_err(|e| VaultError::InternalError(format!("反序列化失败: {}", e)))?;

        Ok(entry_data.metadata)
    }

    /// 更新密钥类型
    pub fn update_key_type(&self, new_type: KeyType) -> VaultResult<()> {
        let serialized = self.get_password()?;

        let mut entry_data: KeychainEntryData = serde_json::from_str(&serialized)
            .map_err(|e| VaultError::InternalError(format!("反序列化失败: {}", e)))?;

        entry_data.metadata.key_type = new_type;
        entry_data.metadata.last_accessed = current_timestamp();

        let updated_serialized = serde_json::to_string(&entry_data)
            .map_err(|e| VaultError::InternalError(format!("序列化失败: {}", e)))?;

        self.store_password(&updated_serialized)
    }

    /// 生成存储键名
    /// 
    /// 为移动平台生成唯一的存储键名
    #[cfg(any(target_os = "android", target_os = "ios"))]
    fn generate_storage_key(&self) -> String {
        format!("{}:{}", self.service, self.account)
    }

    /// 确保移动安全存储已初始化
    /// 
    /// 这是一个异步函数的同步包装器
    #[cfg(any(target_os = "android", target_os = "ios"))]
    fn ensure_mobile_storage_initialized(&self) -> VaultResult<()> {
        // 使用 tokio 的阻塞运行时来执行异步代码
        let rt = tokio::runtime::Runtime::new()
            .map_err(|e| KeychainError::SystemError(format!("创建运行时失败: {}", e)))?;
        
        rt.block_on(async {
            let storage = MOBILE_SECURE_STORAGE.clone();
            let mut storage_guard = storage.write().await;
            
            if !storage_guard.is_initialized().await {
                log::info!("初始化移动平台安全存储");
                storage_guard.initialize().await
                    .map_err(|e| KeychainError::SystemError(format!("初始化安全存储失败: {}", e)))?;
            }
            
            Ok::<(), KeychainError>(())
        })?;
        
        Ok(())
    }

    // 平台特定的实现

    #[cfg(target_os = "macos")]
    fn store_password(&self, password: &str) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry
            .set_password(password)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        Ok(())
    }

    #[cfg(target_os = "macos")]
    fn get_password(&self) -> VaultResult<String> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry.get_password().map_err(|e| match e {
            keyring::Error::NoEntry => KeychainError::KeyNotFound.into(),
            _ => KeychainError::SystemError(e.to_string()).into(),
        })
    }

    #[cfg(target_os = "macos")]
    fn delete_password(&self) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry
            .delete_password()
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        Ok(())
    }

    #[cfg(target_os = "windows")]
    fn store_password(&self, password: &str) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry
            .set_password(password)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        Ok(())
    }

    #[cfg(target_os = "windows")]
    fn get_password(&self) -> VaultResult<String> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry.get_password().map_err(|e| match e {
            keyring::Error::NoEntry => KeychainError::KeyNotFound.into(),
            _ => KeychainError::SystemError(e.to_string()).into(),
        })
    }

    #[cfg(target_os = "windows")]
    fn delete_password(&self) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry
            .delete_password()
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        Ok(())
    }

    #[cfg(target_os = "linux")]
    fn store_password(&self, password: &str) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry
            .set_password(password)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        Ok(())
    }

    #[cfg(target_os = "linux")]
    fn get_password(&self) -> VaultResult<String> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry.get_password().map_err(|e| match e {
            keyring::Error::NoEntry => KeychainError::KeyNotFound.into(),
            _ => KeychainError::SystemError(e.to_string()).into(),
        })
    }

    #[cfg(target_os = "linux")]
    fn delete_password(&self) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service, &self.account)
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        entry
            .delete_password()
            .map_err(|e| KeychainError::SystemError(e.to_string()))?;

        Ok(())
    }

    // 移动平台实现 - 使用 mobile 模块的 KeychainSecureStorage
    #[cfg(any(target_os = "android", target_os = "ios"))]
    fn store_password(&self, password: &str) -> VaultResult<()> {
        log::info!("在移动平台存储密码到安全存储");
        
        // 确保安全存储已初始化
        self.ensure_mobile_storage_initialized()?;
        
        let storage_key = self.generate_storage_key();
        
        // 使用 tokio 运行时执行异步操作
        let rt = tokio::runtime::Runtime::new()
            .map_err(|e| KeychainError::SystemError(format!("创建运行时失败: {}", e)))?;
        
        rt.block_on(async {
            let storage = MOBILE_SECURE_STORAGE.clone();
            let storage_guard = storage.read().await;
            
            storage_guard.store(&storage_key, password).await
                .map_err(|e| KeychainError::SystemError(format!("存储密码失败: {}", e)))?;
            
            log::info!("移动平台密码存储成功: {}", storage_key);
            Ok::<(), KeychainError>(())
        })?;
        
        Ok(())
    }

    #[cfg(any(target_os = "android", target_os = "ios"))]
    fn get_password(&self) -> VaultResult<String> {
        log::info!("从移动平台安全存储获取密码");
        
        // 确保安全存储已初始化
        self.ensure_mobile_storage_initialized()?;
        
        let storage_key = self.generate_storage_key();
        
        // 使用 tokio 运行时执行异步操作
        let rt = tokio::runtime::Runtime::new()
            .map_err(|e| KeychainError::SystemError(format!("创建运行时失败: {}", e)))?;
        
        let password = rt.block_on(async {
            let storage = MOBILE_SECURE_STORAGE.clone();
            let storage_guard = storage.read().await;
            
            let result = storage_guard.retrieve(&storage_key).await
                .map_err(|e| KeychainError::SystemError(format!("获取密码失败: {}", e)))?;
            
            match result {
                Some(password) => {
                    log::info!("移动平台密码获取成功: {}", storage_key);
                    Ok(password)
                }
                None => {
                    log::warn!("移动平台密码未找到: {}", storage_key);
                    Err(KeychainError::KeyNotFound)
                }
            }
        })?;
        
        Ok(password)
    }

    #[cfg(any(target_os = "android", target_os = "ios"))]
    fn delete_password(&self) -> VaultResult<()> {
        log::info!("从移动平台安全存储删除密码");
        
        // 确保安全存储已初始化
        self.ensure_mobile_storage_initialized()?;
        
        let storage_key = self.generate_storage_key();
        
        // 使用 tokio 运行时执行异步操作
        let rt = tokio::runtime::Runtime::new()
            .map_err(|e| KeychainError::SystemError(format!("创建运行时失败: {}", e)))?;
        
        rt.block_on(async {
            let storage = MOBILE_SECURE_STORAGE.clone();
            let storage_guard = storage.read().await;
            
            let deleted = storage_guard.remove(&storage_key).await
                .map_err(|e| KeychainError::SystemError(format!("删除密码失败: {}", e)))?;
            
            if deleted {
                log::info!("移动平台密码删除成功: {}", storage_key);
            } else {
                log::warn!("移动平台密码删除失败，可能不存在: {}", storage_key);
            }
            
            Ok::<(), KeychainError>(())
        })?;
        
        Ok(())
    }
}

/// 内部数据结构，用于存储密钥和元数据
#[derive(Debug, Serialize, Deserialize)]
struct KeychainEntryData {
    metadata: KeychainEntry,
    key: String, // Base64编码的密钥
}

/// 获取当前时间戳
fn current_timestamp() -> i64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs() as i64
}

/// 安全的密钥链密钥容器
pub struct SecureKeychainKey {
    key: [u8; KEY_SIZE],
    metadata: KeychainEntry,
}

impl SecureKeychainKey {
    /// 从密钥链加载密钥
    pub fn load_from_keychain(manager: &KeychainManager) -> VaultResult<Self> {
        let key = manager.get_key()?;
        let metadata = manager.get_metadata()?;

        Ok(Self { key, metadata })
    }

    /// 获取密钥字节
    pub fn key(&self) -> &[u8; KEY_SIZE] {
        &self.key
    }

    /// 获取元数据
    pub fn metadata(&self) -> &KeychainEntry {
        &self.metadata
    }

    /// 验证密钥是否有效
    pub fn is_valid(&self) -> bool {
        !self.key.iter().all(|&b| b == 0)
    }

    /// 获取密钥年龄（秒）
    pub fn age_seconds(&self) -> i64 {
        current_timestamp() - self.metadata.created_at
    }
}

impl Zeroize for SecureKeychainKey {
    fn zeroize(&mut self) {
        self.key.zeroize();
        // 元数据不需要清理，因为它不包含敏感信息
    }
}

impl Drop for SecureKeychainKey {
    fn drop(&mut self) {
        self.zeroize();
    }
}

/// 批量密钥链操作
pub struct BatchKeychainManager {
    base_service: String,
}

impl BatchKeychainManager {
    /// 创建批量管理器
    pub fn new(base_service: &str) -> Self {
        Self {
            base_service: base_service.to_string(),
        }
    }

    /// 存储多个密钥
    pub fn store_keys(&self, keys: &[(&str, &[u8; KEY_SIZE], KeyType)]) -> VaultResult<()> {
        for (account, key, key_type) in keys {
            let manager = KeychainManager::new(&self.base_service, account)?;
            manager.store_key_with_type(key, key_type.clone())?;
        }
        Ok(())
    }

    /// 加载多个密钥
    pub fn load_keys(&self, accounts: &[&str]) -> VaultResult<Vec<(String, SecureKeychainKey)>> {
        let mut results = Vec::new();

        for account in accounts {
            let manager = KeychainManager::new(&self.base_service, account)?;
            match SecureKeychainKey::load_from_keychain(&manager) {
                Ok(key) => results.push((account.to_string(), key)),
                Err(e) => {
                    log::warn!("无法加载账户 {} 的密钥: {}", account, e);
                    // 继续处理其他密钥，不中断整个操作
                }
            }
        }

        Ok(results)
    }

    /// 删除多个密钥
    pub fn delete_keys(&self, accounts: &[&str]) -> VaultResult<()> {
        for account in accounts {
            let manager = KeychainManager::new(&self.base_service, account)?;
            if let Err(e) = manager.delete_key() {
                log::warn!("无法删除账户 {} 的密钥: {}", account, e);
                // 继续处理其他密钥
            }
        }
        Ok(())
    }
}

/// 注册时的密钥管理器，用于管理主密钥、对称密钥和密钥对
/// 支持在 Keychain 和 Stronghold 之间透明切换
#[derive(Clone)]
pub struct RegistrationKeychainManager {
    base_service: String,
    contact: String,
    /// 当前使用的后端类型
    backend_type: Arc<RwLock<BackendType>>,
    /// 统一存储适配器
    storage_adapter: Arc<RwLock<Option<Arc<UnifiedStorageAdapter>>>>,
    /// 故障转移策略
    failover_strategy: FailoverStrategy,
    /// 初始化状态标志
    initialization_in_progress: Arc<RwLock<bool>>,
}

impl RegistrationKeychainManager {
    /// 创建新的注册密钥管理器（不使用缓存）
    /// 
    /// 直接创建新实例，避免同步等待缓存
    /// 推荐使用 get_or_create() 方法来获取缓存实例
    pub fn new(contact: &str) -> Self {
        let manager = Self {
            base_service: "secure-password".to_string(),
            contact: contact.to_string(),
            backend_type: Arc::new(RwLock::new(BackendType::Keychain)), // 默认使用 Keychain
            storage_adapter: Arc::new(RwLock::new(None)),
            failover_strategy: FailoverStrategy::Automatic,
            initialization_in_progress: Arc::new(RwLock::new(false)),
        };

        // 启动缓存清理任务
        Self::start_cache_cleanup_task();

        manager
    }



    /// 异步获取或创建管理器
    pub async fn get_or_create(contact: &str) -> Arc<Self> {
        // 首先尝试从缓存获取
        {
            let cache = GLOBAL_MANAGER_CACHE.read().await;
            if let Some(manager) = cache.get(contact) {
                return Arc::clone(manager);
            }
        }

        // 如果缓存中没有，创建新实例
        let manager = Arc::new(Self {
            base_service: "secure-password".to_string(),
            contact: contact.to_string(),
            backend_type: Arc::new(RwLock::new(BackendType::Keychain)),
            storage_adapter: Arc::new(RwLock::new(None)),
            failover_strategy: FailoverStrategy::Automatic,
            initialization_in_progress: Arc::new(RwLock::new(false)),
        });

        // 将实例放入缓存
        {
            let mut cache = GLOBAL_MANAGER_CACHE.write().await;
            cache.insert(contact.to_string(), Arc::clone(&manager));
        }

        // 启动缓存清理任务
        Self::start_cache_cleanup_task();

        manager
    }

    /// 启动缓存清理任务
    fn start_cache_cleanup_task() {
        // 使用当前运行时启动缓存清理任务，避免创建新的运行时
        if let Ok(handle) = tokio::runtime::Handle::try_current() {
            handle.spawn(async {
                let mut cleanup_started = CACHE_CLEANUP_STARTED.write().await;
                if *cleanup_started {
                    return;
                }
                *cleanup_started = true;
                drop(cleanup_started);

                // 每10分钟清理一次缓存
                let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(600));
                loop {
                    interval.tick().await;
                    Self::cleanup_cache().await;
                }
            });
        } else {
            log::warn!("无法获取当前运行时句柄，跳过缓存清理任务");
        }
    }

    /// 清理缓存中的旧实例
    async fn cleanup_cache() {
        log::debug!("开始清理 RegistrationKeychainManager 缓存");
        let mut cache = GLOBAL_MANAGER_CACHE.write().await;
        let initial_size = cache.len();
        
        // 清理引用计数为1的实例（只有缓存持有引用）
        cache.retain(|contact, manager| {
            let strong_count = Arc::strong_count(manager);
            if strong_count <= 1 {
                log::debug!("清理缓存中的管理器: {}", contact);
                false
            } else {
                true
            }
        });

        let final_size = cache.len();
        if initial_size != final_size {
            log::debug!("缓存清理完成：{} -> {} 个实例", initial_size, final_size);
        }
    }

    /// 创建支持指定后端的注册密钥管理器
    pub async fn new_with_backend(contact: &str, backend: BackendType) -> VaultResult<Self> {
        let manager = Self {
            base_service: "secure-password".to_string(),
            contact: contact.to_string(),
            backend_type: Arc::new(RwLock::new(backend)),
            storage_adapter: Arc::new(RwLock::new(None)),
            failover_strategy: FailoverStrategy::Automatic,
            initialization_in_progress: Arc::new(RwLock::new(false)),
        };

        manager.initialize_adapter().await?;
        Ok(manager)
    }

    /// 创建支持高级配置的注册密钥管理器
    pub async fn new_with_config(
        contact: &str, 
        primary_backend: BackendType,
        failover_strategy: FailoverStrategy
    ) -> VaultResult<Self> {
        let manager = Self {
            base_service: "secure-password".to_string(),
            contact: contact.to_string(),
            backend_type: Arc::new(RwLock::new(primary_backend)),
            storage_adapter: Arc::new(RwLock::new(None)),
            failover_strategy,
            initialization_in_progress: Arc::new(RwLock::new(false)),
        };

        manager.initialize_adapter().await?;
        Ok(manager)
    }

    /// 初始化存储适配器
    async fn initialize_adapter(&self) -> VaultResult<()> {
        // 检查是否正在初始化或已经初始化
        {
            let init_guard = self.initialization_in_progress.read().await;
            if *init_guard {
                log::debug!("适配器正在初始化中，等待完成...");
                drop(init_guard);
                // 等待初始化完成
                while *self.initialization_in_progress.read().await {
                    tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
                }
                return Ok(());
            }
        }

        // 检查是否已经有适配器
        {
            let adapter_guard = self.storage_adapter.read().await;
            if adapter_guard.is_some() {
                return Ok(());
            }
        }

        // 设置初始化标志
        {
            let mut init_guard = self.initialization_in_progress.write().await;
            if *init_guard {
                // 其他线程已经在初始化了
                drop(init_guard);
                while *self.initialization_in_progress.read().await {
                    tokio::time::sleep(tokio::time::Duration::from_millis(10)).await;
                }
                return Ok(());
            }
            *init_guard = true;
        }

        log::debug!("开始初始化存储适配器，联系人: {}", self.contact);

        let result = async {
            use crate::crypto::stronghold::{
                config::create_default_adapter_config,
                factory::StrongholdFactory,
            };

            let backend = *self.backend_type.read().await;
            
            let mut config = create_default_adapter_config();
            config.primary_backend = backend;
            config.failover_strategy = self.failover_strategy.clone();
            
            // 根据后端类型调整配置
            match backend {
                BackendType::Stronghold => {
                    config.enable_stronghold = true;
                    config.enable_keychain = false;
                }
                BackendType::Keychain => {
                    config.enable_stronghold = false;
                    config.enable_keychain = true;
                }
            }

            let factory = StrongholdFactory::default();
            let adapter = factory.create_unified_adapter(Some(config)).await
                .map_err(|e| VaultError::InternalError(format!("初始化存储适配器失败: {}", e)))?;

            *self.storage_adapter.write().await = Some(adapter);
            
            log::debug!("成功初始化存储适配器，后端类型: {:?}，联系人: {}", backend, self.contact);
            Ok(())
        }.await;

        // 清除初始化标志
        {
            let mut init_guard = self.initialization_in_progress.write().await;
            *init_guard = false;
        }

        result
    }

    /// 获取存储适配器，如果未初始化则自动初始化
    async fn get_adapter(&self) -> VaultResult<Arc<UnifiedStorageAdapter>> {
        // 首先快速检查是否已有适配器
        {
            let adapter_guard = self.storage_adapter.read().await;
            if let Some(adapter) = adapter_guard.as_ref() {
                return Ok(Arc::clone(adapter));
            }
        }

        // 如果没有适配器，尝试初始化
        self.initialize_adapter().await?;
        
        // 再次获取适配器
        let adapter_guard = self.storage_adapter.read().await;
        adapter_guard.as_ref()
            .map(|adapter| Arc::clone(adapter))
            .ok_or_else(|| VaultError::InternalError("无法初始化存储适配器".to_string()))
    }

    /// 切换后端类型
    pub async fn switch_backend(&self, new_backend: BackendType) -> VaultResult<()> {
        let current_backend = *self.backend_type.read().await;
        
        if current_backend == new_backend {
            log::info!("后端类型已经是 {:?}，无需切换", new_backend);
            return Ok(());
        }

        log::info!("开始切换后端：{:?} -> {:?}", current_backend, new_backend);

        // 在切换前先备份现有数据
        let migration_data = self.backup_all_keys().await?;

        // 更新后端类型
        *self.backend_type.write().await = new_backend;

        // 重新初始化适配器
        *self.storage_adapter.write().await = None;
        self.initialize_adapter().await?;

        // 迁移数据到新后端
        self.restore_all_keys(migration_data).await?;

        log::info!("成功切换到后端: {:?}", new_backend);
        Ok(())
    }

    /// 备份所有密钥
    async fn backup_all_keys(&self) -> VaultResult<KeyBackup> {
        let mut backup = KeyBackup::default();

        // 尝试备份主密钥
        if let Ok(master_key) = self.get_master_key().await {
            backup.master_key = Some(master_key);
        }

        // 尝试备份对称密钥
        if let Ok(symmetric_key) = self.get_symmetric_key().await {
            backup.symmetric_key = Some(symmetric_key);
        }

        // 尝试备份私钥
        if let Ok(private_key) = self.get_private_key().await {
            backup.private_key = Some(private_key);
        }

        // 尝试备份公钥
        if let Ok(public_key) = self.get_public_key().await {
            backup.public_key = Some(public_key);
        }

        log::debug!("备份了 {} 个密钥", backup.count_keys());
        Ok(backup)
    }

    /// 恢复所有密钥
    async fn restore_all_keys(&self, backup: KeyBackup) -> VaultResult<()> {
        let mut restored_count = 0;

        if let Some(master_key) = backup.master_key {
            if let Err(e) = self.store_master_key(&master_key).await {
                log::warn!("恢复主密钥失败: {}", e);
            } else {
                restored_count += 1;
            }
        }

        if let Some(symmetric_key) = backup.symmetric_key {
            if let Err(e) = self.store_symmetric_key(&symmetric_key).await {
                log::warn!("恢复对称密钥失败: {}", e);
            } else {
                restored_count += 1;
            }
        }

        if let Some(private_key) = backup.private_key {
            if let Err(e) = self.store_private_key(&private_key).await {
                log::warn!("恢复私钥失败: {}", e);
            } else {
                restored_count += 1;
            }
        }

        if let Some(public_key) = backup.public_key {
            if let Err(e) = self.store_public_key(&public_key).await {
                log::warn!("恢复公钥失败: {}", e);
            } else {
                restored_count += 1;
            }
        }

        log::info!("成功恢复了 {} 个密钥", restored_count);
        Ok(())
    }

    /// 获取当前使用的后端类型
    pub async fn get_backend_type(&self) -> BackendType {
        *self.backend_type.read().await
    }

    /// 检查后端是否可用
    pub async fn is_backend_available(&self, backend: BackendType) -> bool {
        backend.is_available()
    }

    /// 获取后端健康状态
    pub async fn get_backend_health(&self) -> VaultResult<Vec<crate::crypto::stronghold::adapter::HealthCheckResult>> {
        let adapter = self.get_adapter().await?;
        Ok(adapter.get_backend_health().await)
    }

    /// 存储主密钥
    pub async fn store_master_key(&self, master_key: &[u8; KEY_SIZE]) -> VaultResult<()> {
        let adapter = self.get_adapter().await?;
        let key = format!("{}-master", self.contact);
        adapter.store(&key, master_key).await
            .map_err(|e| VaultError::InternalError(format!("存储主密钥失败: {}", e)))?;
        
        log::debug!("成功存储主密钥: {}", key);
        Ok(())
    }

    /// 存储对称密钥
    pub async fn store_symmetric_key(&self, symmetric_key: &[u8; KEY_SIZE]) -> VaultResult<()> {
        let adapter = self.get_adapter().await?;
        let key = format!("{}-symmetric", self.contact);
        adapter.store(&key, symmetric_key).await
            .map_err(|e| VaultError::InternalError(format!("存储对称密钥失败: {}", e)))?;
        
        log::debug!("成功存储对称密钥: {}", key);
        Ok(())
    }

    /// 存储私钥（Base64编码的字符串）
    pub async fn store_private_key(&self, private_key: &str) -> VaultResult<()> {
        let adapter = self.get_adapter().await?;
        let key = format!("{}-private", self.contact);
        let private_key_bytes = private_key.as_bytes();
        adapter.store(&key, private_key_bytes).await
            .map_err(|e| VaultError::InternalError(format!("存储私钥失败: {}", e)))?;
        
        log::debug!("成功存储私钥: {}", key);
        Ok(())
    }

    /// 存储公钥（Base64编码的字符串）
    pub async fn store_public_key(&self, public_key: &str) -> VaultResult<()> {
        let adapter = self.get_adapter().await?;
        let key = format!("{}-public", self.contact);
        let public_key_bytes = public_key.as_bytes();
        adapter.store(&key, public_key_bytes).await
            .map_err(|e| VaultError::InternalError(format!("存储公钥失败: {}", e)))?;
        
        log::debug!("成功存储公钥: {}", key);
        Ok(())
    }

    /// 获取主密钥
    pub async fn get_master_key(&self) -> VaultResult<[u8; KEY_SIZE]> {
        let adapter = self.get_adapter().await?;
        let key = format!("{}-master", self.contact);
        
        match adapter.get(&key).await {
            Ok(Some(data)) => {
                if data.len() != KEY_SIZE {
                    return Err(VaultError::InternalError(format!(
                        "无效的主密钥长度: 期望{}字节，实际{}字节",
                        KEY_SIZE, data.len()
                    )));
                }
                let mut master_key = [0u8; KEY_SIZE];
                master_key.copy_from_slice(&data);
                Ok(master_key)
            }
            Ok(None) => Err(VaultError::InternalError("主密钥未找到".to_string())),
            Err(e) => Err(VaultError::InternalError(format!("获取主密钥失败: {}", e))),
        }
    }

    /// 获取对称密钥
    pub async fn get_symmetric_key(&self) -> VaultResult<[u8; KEY_SIZE]> {
        let adapter = self.get_adapter().await?;
        let key = format!("{}-symmetric", self.contact);
        
        match adapter.get(&key).await {
            Ok(Some(data)) => {
                if data.len() != KEY_SIZE {
                    return Err(VaultError::InternalError(format!(
                        "无效的对称密钥长度: 期望{}字节，实际{}字节",
                        KEY_SIZE, data.len()
                    )));
                }
                let mut symmetric_key = [0u8; KEY_SIZE];
                symmetric_key.copy_from_slice(&data);
                Ok(symmetric_key)
            }
            Ok(None) => Err(VaultError::InternalError("对称密钥未找到".to_string())),
            Err(e) => Err(VaultError::InternalError(format!("获取对称密钥失败: {}", e))),
        }
    }

    /// 获取私钥
    pub async fn get_private_key(&self) -> VaultResult<String> {
        let adapter = self.get_adapter().await?;
        let key = format!("{}-private", self.contact);
        
        match adapter.get(&key).await {
            Ok(Some(data)) => {
                String::from_utf8(data)
                    .map_err(|e| VaultError::InternalError(format!("私钥数据格式无效: {}", e)))
            }
            Ok(None) => Err(VaultError::InternalError("私钥未找到".to_string())),
            Err(e) => Err(VaultError::InternalError(format!("获取私钥失败: {}", e))),
        }
    }

    /// 获取公钥
    pub async fn get_public_key(&self) -> VaultResult<String> {
        let adapter = self.get_adapter().await?;
        let key = format!("{}-public", self.contact);
        
        match adapter.get(&key).await {
            Ok(Some(data)) => {
                String::from_utf8(data)
                    .map_err(|e| VaultError::InternalError(format!("公钥数据格式无效: {}", e)))
            }
            Ok(None) => Err(VaultError::InternalError("公钥未找到".to_string())),
            Err(e) => Err(VaultError::InternalError(format!("获取公钥失败: {}", e))),
        }
    }

    /// 检查所有密钥是否存在
    pub async fn all_keys_exist(&self) -> bool {
        let master_exists = self.get_master_key().await.is_ok();
        let symmetric_exists = self.get_symmetric_key().await.is_ok();
        let private_exists = self.get_private_key().await.is_ok();
        let public_exists = self.get_public_key().await.is_ok();

        master_exists && symmetric_exists && private_exists && public_exists
    }

    /// 删除所有密钥
    pub async fn delete_all_keys(&self) -> VaultResult<()> {
        let adapter = self.get_adapter().await?;
        let keys = vec![
            format!("{}-master", self.contact),
            format!("{}-symmetric", self.contact),
            format!("{}-private", self.contact),
            format!("{}-public", self.contact),
        ];

        for key in keys {
            if let Err(e) = adapter.remove(&key).await {
                log::warn!("删除密钥失败 {}: {}", key, e);
                // 继续删除其他密钥
            }
        }

        Ok(())
    }

    /// 列出所有存储的密钥
    pub async fn list_keys(&self) -> VaultResult<Vec<String>> {
        let adapter = self.get_adapter().await?;
        adapter.list_keys().await
            .map_err(|e| VaultError::InternalError(format!("列出密钥失败: {}", e)))
    }

    /// 检查后端一致性
    pub async fn check_backend_consistency(&self) -> VaultResult<Vec<String>> {
        let adapter = self.get_adapter().await?;
        Ok(adapter.check_backend_consistency().await)
    }

    /// 强制后端故障转移
    pub async fn force_failover(&self) -> VaultResult<()> {
        let current_backend = *self.backend_type.read().await;
        
        // 确定目标后端
        let target_backend = match current_backend {
            BackendType::Keychain => BackendType::Stronghold,
            BackendType::Stronghold => BackendType::Keychain,
        };
        
        // 检查目标后端是否可用
        if !self.is_backend_available(target_backend).await {
            return Err(VaultError::InternalError(format!(
                "目标后端 {:?} 不可用，无法进行故障转移", target_backend
            )));
        }
        
        // 执行后端切换
        self.switch_backend(target_backend).await?;
        
        log::info!("故障转移完成：{:?} -> {:?}", current_backend, target_backend);
        Ok(())
    }

    /// 获取故障转移统计信息
    pub async fn get_failover_stats(&self) -> VaultResult<u32> {
        let adapter = self.get_adapter().await?;
        Ok(adapter.get_failover_count().await)
    }
}

/// 密钥备份结构
#[derive(Debug, Default)]
struct KeyBackup {
    master_key: Option<[u8; KEY_SIZE]>,
    symmetric_key: Option<[u8; KEY_SIZE]>,
    private_key: Option<String>,
    public_key: Option<String>,
}

impl KeyBackup {
    /// 计算备份的密钥数量
    fn count_keys(&self) -> usize {
        let mut count = 0;
        if self.master_key.is_some() { count += 1; }
        if self.symmetric_key.is_some() { count += 1; }
        if self.private_key.is_some() { count += 1; }
        if self.public_key.is_some() { count += 1; }
        count
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_keychain_manager_creation() {
        let manager = KeychainManager::new("test-service", "test-user").unwrap();
        assert_eq!(manager.service, "test-service");
        assert_eq!(manager.account, "test-user");

        // 测试无效输入
        assert!(KeychainManager::new("", "user").is_err());
        assert!(KeychainManager::new("service", "").is_err());
    }

    #[test]
    fn test_key_type() {
        let master_key = KeyType::MasterKey;
        assert_eq!(master_key.description(), "主密钥");

        let custom_key = KeyType::Custom("自定义密钥".to_string());
        assert_eq!(custom_key.description(), "自定义密钥");
    }

    #[test]
    fn test_keychain_entry_data() {
        let entry = KeychainEntry {
            service: "test".to_string(),
            account: "user".to_string(),
            created_at: current_timestamp(),
            last_accessed: current_timestamp(),
            key_type: KeyType::MasterKey,
        };

        let entry_data = KeychainEntryData {
            metadata: entry,
            key: "dGVzdGtleQ==".to_string(), // "testkey" in base64
        };

        let serialized = serde_json::to_string(&entry_data).unwrap();
        let deserialized: KeychainEntryData = serde_json::from_str(&serialized).unwrap();

        assert_eq!(deserialized.key, "dGVzdGtleQ==");
        assert_eq!(deserialized.metadata.service, "test");
    }

    // 移动平台特定测试
    #[cfg(any(target_os = "android", target_os = "ios"))]
    #[test]
    fn test_mobile_keychain_manager_creation() {
        let manager = KeychainManager::new("mobile-test-service", "mobile-test-user").unwrap();
        assert_eq!(manager.service, "mobile-test-service");
        assert_eq!(manager.account, "mobile-test-user");
        
        // 测试存储键名生成
        let storage_key = manager.generate_storage_key();
        assert_eq!(storage_key, "mobile-test-service:mobile-test-user");
    }

    #[cfg(any(target_os = "android", target_os = "ios"))]
    #[test]
    fn test_mobile_error_conversion() {
        use crate::mobile::MobileError;
        
        // 测试各种错误类型的转换
        let unsupported_error = MobileError::unsupported_platform("test feature");
        let keychain_error: KeychainError = unsupported_error.into();
        assert!(matches!(keychain_error, KeychainError::ServiceUnavailable));
        
        let storage_error = MobileError::secure_storage_error("test reason");
        let keychain_error: KeychainError = storage_error.into();
        assert!(matches!(keychain_error, KeychainError::SystemError(_)));
        
        let permission_error = MobileError::permission_error("test permission");
        let keychain_error: KeychainError = permission_error.into();
        assert!(matches!(keychain_error, KeychainError::AccessDenied));
    }

    #[cfg(any(target_os = "android", target_os = "ios"))]
    #[test]
    fn test_mobile_keychain_basic_operations() {
        // 测试基本的密钥链操作（不涉及实际存储）
        let manager = KeychainManager::new("test-mobile-service", "test-mobile-user").unwrap();
        
        // 测试密钥生成和序列化
        let test_key = [1u8; KEY_SIZE];
        let encoded_key = BASE64_STANDARD.encode(&test_key);
        
        let metadata = KeychainEntry {
            service: manager.service.clone(),
            account: manager.account.clone(),
            created_at: current_timestamp(),
            last_accessed: current_timestamp(),
            key_type: KeyType::MasterKey,
        };

        let entry_data = KeychainEntryData {
            metadata,
            key: encoded_key,
        };

        // 测试序列化和反序列化
        let serialized = serde_json::to_string(&entry_data).unwrap();
        let deserialized: KeychainEntryData = serde_json::from_str(&serialized).unwrap();
        
        assert_eq!(deserialized.metadata.service, "test-mobile-service");
        assert_eq!(deserialized.metadata.account, "test-mobile-user");
        assert!(matches!(deserialized.metadata.key_type, KeyType::MasterKey));
        
        // 测试密钥解码
        let decoded_key = BASE64_STANDARD.decode(&deserialized.key).unwrap();
        assert_eq!(decoded_key.len(), KEY_SIZE);
        assert_eq!(decoded_key, test_key);
        
        println!("✅ 移动平台密钥链基本操作测试通过");
    }

    // 注意：实际的密钥链测试需要在支持的平台上运行，并且可能需要用户交互
    // 这里只测试不需要实际密钥链访问的功能

    #[test]
    fn test_batch_keychain_manager() {
        let batch_manager = BatchKeychainManager::new("test-batch-service");
        assert_eq!(batch_manager.base_service, "test-batch-service");
    }

    #[tokio::test]
    async fn test_registration_keychain_manager() {
        let reg_manager = RegistrationKeychainManager::get_or_create("<EMAIL>").await;
        assert_eq!(reg_manager.base_service, "secure-password");
        assert_eq!(reg_manager.contact, "<EMAIL>");
    }
}
