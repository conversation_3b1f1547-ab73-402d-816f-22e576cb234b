// Stronghold 密码提供和密钥管理模块
// 负责安全地管理和提供 Stronghold 所需的密钥

use std::sync::Arc;
use log;
use tokio::sync::RwLock;
use zeroize::Zeroize;

use super::{StrongholdResult, StrongholdError};

/// 密码强度等级
#[derive(Debug, Clone, Copy, PartialEq, Eq, PartialOrd, Ord)]
pub enum PasswordStrength {
    /// 弱密码
    Weak,
    /// 中等密码
    Medium,
    /// 强密码
    Strong,
    /// 非常强的密码
    VeryStrong,
}

/// 密码来源
#[derive(Debug, Clone, PartialEq, Eq)]
pub enum PasswordSource {
    /// 环境变量
    Environment(String),
    /// 用户输入
    UserInput,
    /// 系统密钥链
    SystemKeychain,
    /// 配置文件
    ConfigFile(String),
    /// 默认密码（仅开发环境）
    Default,
}

/// 安全密码容器
/// 
/// 使用 zeroize 确保密码在内存中被安全清除
#[derive(Debug)]
struct SecurePassword {
    /// 密码数据
    data: Vec<u8>,
    /// 密码来源
    source: PasswordSource,
    /// 创建时间
    created_at: std::time::Instant,
}

impl Drop for SecurePassword {
    fn drop(&mut self) {
        self.data.zeroize();
    }
}

impl SecurePassword {
    /// 创建新的安全密码
    fn new(password: String, source: PasswordSource) -> Self {
        Self {
            data: password.into_bytes(),
            source,
            created_at: std::time::Instant::now(),
        }
    }

    /// 获取密码数据的副本
    fn get_bytes(&self) -> Vec<u8> {
        self.data.clone()
    }

    /// 获取密码强度
    fn strength(&self) -> PasswordStrength {
        evaluate_password_strength(&self.data)
    }

    /// 检查密码是否过期（基于创建时间）
    fn is_expired(&self, max_age: std::time::Duration) -> bool {
        self.created_at.elapsed() > max_age
    }
}

/// 密码提供器
/// 
/// 负责从各种来源安全地获取和管理 Stronghold 密码
#[derive(Debug)]
pub struct PasswordProvider {
    /// 当前密码
    current_password: Arc<RwLock<Option<SecurePassword>>>,
    /// 密码最大使用时间
    max_password_age: std::time::Duration,
    /// 是否启用密码强度检查
    enforce_strength: bool,
    /// 最小密码强度要求
    min_strength: PasswordStrength,
}

impl Default for PasswordProvider {
    fn default() -> Self {
        Self {
            current_password: Arc::new(RwLock::new(None)),
            max_password_age: std::time::Duration::from_secs(3600), // 1小时
            enforce_strength: true,
            min_strength: PasswordStrength::Strong,
        }
    }
}

impl PasswordProvider {
    /// 创建新的密码提供器
    pub fn new() -> Self {
        Self::default()
    }

    /// 创建用于开发环境的密码提供器
    pub fn for_development() -> Self {
        Self {
            current_password: Arc::new(RwLock::new(None)),
            max_password_age: std::time::Duration::from_secs(86400), // 24小时
            enforce_strength: false, // 开发环境不强制密码强度
            min_strength: PasswordStrength::Weak,
        }
    }

    /// 创建用于生产环境的密码提供器
    pub fn for_production() -> Self {
        Self {
            current_password: Arc::new(RwLock::new(None)),
            max_password_age: std::time::Duration::from_secs(1800), // 30分钟
            enforce_strength: true,
            min_strength: PasswordStrength::VeryStrong,
        }
    }

    /// 设置密码（从字符串）
    pub async fn set_password(&self, password: String, source: PasswordSource) -> StrongholdResult<()> {
        // 验证密码强度
        if self.enforce_strength {
            let strength = evaluate_password_strength(password.as_bytes());
            if strength < self.min_strength {
                return Err(StrongholdError::ConfigValidationFailed(
                    format!("密码强度不足，要求: {:?}，实际: {:?}", self.min_strength, strength)
                ));
            }
        }

        let secure_password = SecurePassword::new(password, source);
        let mut current = self.current_password.write().await;
        *current = Some(secure_password);

        log::info!("密码已设置，来源: {:?}", current.as_ref().unwrap().source);
        Ok(())
    }

    /// 从环境变量获取密码
    pub async fn load_from_environment(&self, var_name: &str) -> StrongholdResult<()> {
        let password = std::env::var(var_name)
            .map_err(|_| StrongholdError::ConfigValidationFailed(
                format!("环境变量 {} 未设置", var_name)
            ))?;

        if password.is_empty() {
            return Err(StrongholdError::ConfigValidationFailed(
                format!("环境变量 {} 为空", var_name)
            ));
        }

        self.set_password(password, PasswordSource::Environment(var_name.to_string())).await
    }

    /// 使用默认密码（仅开发环境）
    pub async fn use_default_password(&self) -> StrongholdResult<()> {
        let default_password = "secure_default_password_2024!".to_string();
        
        log::warn!("使用默认密码 - 仅适用于开发环境！");
        
        self.set_password(default_password, PasswordSource::Default).await
    }

    /// 获取密码用于 Stronghold 插件
    pub async fn get_password_for_stronghold(&self, _salt: &str) -> StrongholdResult<Vec<u8>> {
        let current = self.current_password.read().await;
        
        match current.as_ref() {
            Some(secure_password) => {
                // 检查密码是否过期
                if secure_password.is_expired(self.max_password_age) {
                    log::warn!("当前密码已过期，需要重新设置");
                    return Err(StrongholdError::ConfigValidationFailed(
                        "密码已过期，请重新设置".to_string()
                    ));
                }

                log::debug!("提供 Stronghold 密码，来源: {:?}", secure_password.source);
                Ok(secure_password.get_bytes())
            }
            None => {
                log::error!("未设置密码，尝试从环境变量加载");
                
                // 尝试从环境变量加载
                drop(current); // 释放读锁
                
                if let Ok(()) = self.load_from_environment("STRONGHOLD_PASSWORD").await {
                    // 重新获取密码，避免递归
                    let current = self.current_password.read().await;
                    if let Some(secure_password) = current.as_ref() {
                        return Ok(secure_password.get_bytes());
                    }
                }

                // 如果不是生产环境，使用默认密码
                if !self.enforce_strength {
                    self.use_default_password().await?;
                    // 重新获取密码，避免递归
                    let current = self.current_password.read().await;
                    if let Some(secure_password) = current.as_ref() {
                        return Ok(secure_password.get_bytes());
                    }
                }

                Err(StrongholdError::ConfigValidationFailed(
                    "未设置密码且无法从环境变量加载".to_string()
                ))
            }
        }
    }

    /// 获取密码信息（不包含实际密码）
    pub async fn get_password_info(&self) -> Option<(PasswordSource, PasswordStrength, std::time::Duration)> {
        let current = self.current_password.read().await;
        current.as_ref().map(|p| (
            p.source.clone(),
            p.strength(),
            p.created_at.elapsed()
        ))
    }

    /// 清除当前密码
    pub async fn clear_password(&self) {
        let mut current = self.current_password.write().await;
        if let Some(mut password) = current.take() {
            password.data.zeroize();
            log::info!("密码已清除");
        }
    }

    /// 验证密码是否可用
    pub async fn validate_password(&self) -> StrongholdResult<()> {
        let current = self.current_password.read().await;
        
        match current.as_ref() {
            Some(secure_password) => {
                if secure_password.is_expired(self.max_password_age) {
                    Err(StrongholdError::ConfigValidationFailed("密码已过期".to_string()))
                } else if self.enforce_strength && secure_password.strength() < self.min_strength {
                    Err(StrongholdError::ConfigValidationFailed("密码强度不足".to_string()))
                } else {
                    Ok(())
                }
            }
            None => Err(StrongholdError::ConfigValidationFailed("未设置密码".to_string()))
        }
    }

    /// 更新密码提供器配置
    pub fn update_config(
        &mut self,
        max_age: Option<std::time::Duration>,
        enforce_strength: Option<bool>,
        min_strength: Option<PasswordStrength>,
    ) {
        if let Some(age) = max_age {
            self.max_password_age = age;
        }
        if let Some(enforce) = enforce_strength {
            self.enforce_strength = enforce;
        }
        if let Some(strength) = min_strength {
            self.min_strength = strength;
        }
    }
}

/// 全局密码提供器实例
static PASSWORD_PROVIDER: once_cell::sync::Lazy<Arc<RwLock<PasswordProvider>>> = 
    once_cell::sync::Lazy::new(|| Arc::new(RwLock::new(PasswordProvider::new())));

/// 获取全局密码提供器
pub fn get_global_password_provider() -> Arc<RwLock<PasswordProvider>> {
    PASSWORD_PROVIDER.clone()
}

/// 初始化全局密码提供器
pub async fn initialize_password_provider(for_production: bool) -> StrongholdResult<()> {
    let provider = if for_production {
        PasswordProvider::for_production()
    } else {
        PasswordProvider::for_development()
    };

    let mut global_provider = PASSWORD_PROVIDER.write().await;
    *global_provider = provider;

    log::info!("密码提供器已初始化，生产模式: {}", for_production);
    Ok(())
}

/// 为 Tauri Stronghold 插件提供密码的函数
/// 
/// 这个函数被 Tauri Stronghold 插件调用以获取密码
pub fn stronghold_password_provider(password: &str) -> Vec<u8> {
    // 使用 tokio 的阻塞运行时来处理异步调用
    let rt = tokio::runtime::Handle::try_current()
        .or_else(|_| {
            // 如果没有当前运行时，创建一个新的
            tokio::runtime::Runtime::new()
                .map(|rt| rt.handle().clone())
        });

    match rt {
        Ok(handle) => {
            // 在异步上下文中获取密码
            match handle.block_on(async {
                // 直接使用传入的密码进行哈希处理
                use argon2::{Argon2, PasswordHasher};
                use argon2::password_hash::SaltString;

                // 创建 Argon2 实例
                let argon2 = Argon2::default();

                // 使用密码本身作为盐的基础，结合固定的盐
                let fixed_salt = "secure_password_salt_2024";
                let combined_salt = format!("{}_{}", fixed_salt, password.chars().take(8).collect::<String>());
                
                // 创建固定长度的盐
                let salt = SaltString::encode_b64(combined_salt.as_bytes())
                    .map_err(|e| format!("创建盐失败: {}", e))?;

                // 生成密码哈希
                let password_hash = argon2.hash_password(password.as_bytes(), &salt)
                    .map_err(|e| format!("密码哈希失败: {}", e))?;

                // 提取哈希值的字节（取前32字节）
                let hash_ref = password_hash.hash.unwrap();
                let hash_bytes = hash_ref.as_bytes();
                Ok::<Vec<u8>, String>(hash_bytes[..32.min(hash_bytes.len())].to_vec())
            }) {
                Ok(hashed) => {
                    log::info!("成功创建 Stronghold 密码哈希（长度: {} 字节）", hashed.len());
                    hashed
                }
                Err(e) => {
                    log::error!("获取 Stronghold 密码失败: {}", e);
                    // 返回默认密码哈希作为后备方案
                    let fallback = "secure_default_password_2024!";
                    log::warn!("使用后备密码");
                    
                    // 对后备密码也进行简单哈希处理
                    use std::collections::hash_map::DefaultHasher;
                    use std::hash::{Hash, Hasher};
                    
                    let mut hasher = DefaultHasher::new();
                    fallback.hash(&mut hasher);
                    let hash = hasher.finish();
                    
                    // 将 u64 转换为 32 字节数组
                    let mut result = vec![0u8; 32];
                    let hash_bytes = hash.to_le_bytes();
                    for (i, &byte) in hash_bytes.iter().enumerate() {
                        result[i % 32] ^= byte;
                    }
                    result
                }
            }
        }
        Err(e) => {
            log::error!("无法获取 tokio 运行时: {}", e);
            // 返回默认密码哈希
            let fallback = "secure_default_password_2024!";
            
            use std::collections::hash_map::DefaultHasher;
            use std::hash::{Hash, Hasher};
            
            let mut hasher = DefaultHasher::new();
            fallback.hash(&mut hasher);
            let hash = hasher.finish();
            
            // 将 u64 转换为 32 字节数组
            let mut result = vec![0u8; 32];
            let hash_bytes = hash.to_le_bytes();
            for (i, &byte) in hash_bytes.iter().enumerate() {
                result[i % 32] ^= byte;
            }
            result
        }
    }
}

/// 评估密码强度
fn evaluate_password_strength(password: &[u8]) -> PasswordStrength {
    let password_str = String::from_utf8_lossy(password);
    let len = password_str.len();
    
    if len < 8 {
        return PasswordStrength::Weak;
    }

    let mut score = 0;
    
    // 长度评分
    if len >= 12 { score += 2; }
    else if len >= 8 { score += 1; }
    
    // 字符类型评分
    if password_str.chars().any(|c| c.is_ascii_lowercase()) { score += 1; }
    if password_str.chars().any(|c| c.is_ascii_uppercase()) { score += 1; }
    if password_str.chars().any(|c| c.is_ascii_digit()) { score += 1; }
    if password_str.chars().any(|c| !c.is_alphanumeric()) { score += 1; }
    
    // 复杂性评分
    if len >= 16 { score += 1; }
    if password_str.chars().filter(|c| !c.is_alphanumeric()).count() >= 3 { score += 1; }
    
    match score {
        0..=2 => PasswordStrength::Weak,
        3..=4 => PasswordStrength::Medium,
        5..=6 => PasswordStrength::Strong,
        _ => PasswordStrength::VeryStrong,
    }
}

/// 为后端管理提供密码验证功能
pub async fn validate_backend_password(backend_name: &str) -> StrongholdResult<bool> {
    let provider = PASSWORD_PROVIDER.read().await;
    
    match provider.validate_password().await {
        Ok(()) => {
            log::debug!("后端 {} 的密码验证通过", backend_name);
            Ok(true)
        }
        Err(e) => {
            log::warn!("后端 {} 的密码验证失败: {}", backend_name, e);
            Ok(false)
        }
    }
}

/// 为 enable_stronghold_backend 提供密码准备功能
pub async fn prepare_password_for_backend_enable() -> StrongholdResult<()> {
    let provider = PASSWORD_PROVIDER.read().await;
    
    // 首先检查当前密码是否可用
    match provider.validate_password().await {
        Ok(()) => {
            log::info!("当前密码可用，后端启用的密码准备完成");
            return Ok(());
        }
        Err(_) => {
            log::info!("当前密码不可用，尝试重新设置密码");
        }
    }
    
    drop(provider); // 释放读锁
    
    // 尝试重新加载密码
    let provider = PASSWORD_PROVIDER.read().await;
    
    // 先尝试从环境变量加载
    if let Ok(_) = std::env::var("STRONGHOLD_PASSWORD") {
        if let Ok(_) = provider.load_from_environment("STRONGHOLD_PASSWORD").await {
            log::info!("从环境变量重新加载密码成功");
            return Ok(());
        }
    }
    
    // 如果从环境变量加载失败，检查是否可以使用默认密码
    if !provider.enforce_strength {
        if let Ok(_) = provider.use_default_password().await {
            log::info!("使用默认密码作为后备方案");
            return Ok(());
        }
    }
    
    // 如果都失败了，返回错误
    Err(StrongholdError::ConfigValidationFailed(
        "无法为后端启用准备密码".to_string()
    ))
}

/// 为 disable_stronghold_backend 提供密码清理功能
pub async fn cleanup_password_for_backend_disable() -> StrongholdResult<()> {
    // 在禁用后端时，我们通常不清理密码，因为可能还有其他后端在使用
    // 但我们可以记录这个事件
    log::info!("后端禁用，密码保持可用状态");
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_password_strength_evaluation() {
        assert_eq!(evaluate_password_strength(b"123"), PasswordStrength::Weak);
        assert_eq!(evaluate_password_strength(b"password"), PasswordStrength::Weak);
        assert_eq!(evaluate_password_strength(b"Password123"), PasswordStrength::Medium);
        assert_eq!(evaluate_password_strength(b"Password123!"), PasswordStrength::Strong);
        assert_eq!(evaluate_password_strength(b"VeryStrongPassword123!@#$"), PasswordStrength::VeryStrong);
    }

    #[tokio::test]
    async fn test_password_provider_creation() {
        let provider = PasswordProvider::new();
        assert!(provider.get_password_info().await.is_none());
    }

    #[tokio::test]
    async fn test_password_provider_set_and_get() {
        let provider = PasswordProvider::for_development();
        
        // 设置密码
        let password = "TestPassword123!".to_string();
        let result = provider.set_password(password.clone(), PasswordSource::UserInput).await;
        assert!(result.is_ok());

        // 验证密码信息
        let info = provider.get_password_info().await;
        assert!(info.is_some());
        
        let (source, strength, _age) = info.unwrap();
        assert_eq!(source, PasswordSource::UserInput);
        assert!(matches!(strength, PasswordStrength::Strong | PasswordStrength::VeryStrong));
    }

    #[tokio::test]
    async fn test_password_provider_validation() {
        let provider = PasswordProvider::for_development();
        
        // 未设置密码时验证应该失败
        assert!(provider.validate_password().await.is_err());
        
        // 设置密码后验证应该成功
        provider.set_password("TestPassword123!".to_string(), PasswordSource::UserInput).await.unwrap();
        assert!(provider.validate_password().await.is_ok());
    }

    #[tokio::test]
    async fn test_password_provider_clear() {
        let provider = PasswordProvider::for_development();
        
        // 设置密码
        provider.set_password("TestPassword123!".to_string(), PasswordSource::UserInput).await.unwrap();
        assert!(provider.get_password_info().await.is_some());
        
        // 清除密码
        provider.clear_password().await;
        assert!(provider.get_password_info().await.is_none());
    }

    #[tokio::test]
    async fn test_backend_password_functions() {
        // 初始化密码提供器
        initialize_password_provider(false).await.unwrap();
        
        // 准备密码
        let result = prepare_password_for_backend_enable().await;
        assert!(result.is_ok());
        
        // 验证密码
        let valid = validate_backend_password("test_backend").await.unwrap();
        assert!(valid);
        
        // 清理密码
        let result = cleanup_password_for_backend_disable().await;
        assert!(result.is_ok());
    }
} 