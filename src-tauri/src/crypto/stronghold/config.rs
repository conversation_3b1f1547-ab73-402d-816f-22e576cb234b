// Stronghold 配置管理模块
// 定义 Stronghold 和适配器的配置结构体

use crate::crypto::stronghold::error::{StrongholdError, StrongholdResult, RetryStrategy};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use std::time::Duration;

/// 后端存储类型
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum BackendType {
    /// Stronghold 后端
    Stronghold,
    /// 系统密钥链后端
    Keychain,
}

impl BackendType {
    /// 获取后端名称
    pub fn name(&self) -> &'static str {
        match self {
            BackendType::Stronghold => "Stronghold",
            BackendType::Keychain => "Keychain",
        }
    }
    
    /// 检查后端是否在当前平台可用
    pub fn is_available(&self) -> bool {
        match self {
            BackendType::Stronghold => true, // Stronghold 在所有平台都可用
            BackendType::Keychain => {
                // 检查系统密钥链是否可用
                #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
                {
                    true
                }
                #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
                {
                    false
                }
            }
        }
    }
}

/// 故障转移策略
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum FailoverStrategy {
    /// 不进行故障转移，失败时直接返回错误
    None,
    /// 自动故障转移到备用后端
    Automatic,
    /// 手动故障转移，需要用户确认
    Manual,
    /// 智能故障转移，根据错误类型决定是否转移
    Smart,
}

/// Stronghold 配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrongholdConfig {
    /// Stronghold 文件存储路径
    pub vault_path: PathBuf,
    
    /// 客户端名称
    pub client_name: String,
    
    /// 密码策略配置
    pub password_policy: PasswordPolicy,
    
    /// 快照间隔（秒）
    pub snapshot_interval: Option<u64>,
    
    /// 操作超时时间（毫秒）
    pub timeout_ms: u64,
    
    /// 重试策略
    pub retry_strategy: RetryStrategy,
    
    /// 是否启用自动保存
    pub auto_save: bool,
    
    /// 是否启用压缩
    pub enable_compression: bool,
    
    /// 最大内存使用量（MB）
    pub max_memory_mb: Option<u64>,
    
    /// 是否启用调试日志
    pub debug_logging: bool,
}

/// 密码策略配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PasswordPolicy {
    /// 最小密码长度
    pub min_length: usize,
    
    /// 最大密码长度
    pub max_length: usize,
    
    /// 是否要求包含数字
    pub require_numbers: bool,
    
    /// 是否要求包含特殊字符
    pub require_special_chars: bool,
    
    /// 是否要求包含大写字母
    pub require_uppercase: bool,
    
    /// 是否要求包含小写字母
    pub require_lowercase: bool,
    
    /// 密码过期时间（天）
    pub expiry_days: Option<u32>,
}

impl Default for PasswordPolicy {
    fn default() -> Self {
        Self {
            min_length: 8,
            max_length: 128,
            require_numbers: true,
            require_special_chars: true,
            require_uppercase: true,
            require_lowercase: true,
            expiry_days: None,
        }
    }
}

impl Default for StrongholdConfig {
    fn default() -> Self {
        // 获取应用数据目录的绝对路径
        let vault_path = if let Some(data_dir) = std::env::var_os("APPDATA")
            .or_else(|| std::env::var_os("HOME")) 
        {
            // Windows: APPDATA, macOS/Linux: HOME
            let mut path = PathBuf::from(data_dir);
            #[cfg(target_os = "windows")]
            path.push("com.secure-password.app");
            #[cfg(target_os = "macos")]
            {
                path.push("Library");
                path.push("Application Support");
                path.push("com.secure-password.app");
            }
            #[cfg(target_os = "linux")]
            {
                path.push(".local");
                path.push("share");
                path.push("com.secure-password.app");
            }
            path.push("vault.stronghold");
            path
        } else {
            // 如果无法获取系统目录，回退到当前目录
            PathBuf::from("vault.stronghold")
        };

        Self {
            vault_path,
            client_name: "default_client".to_string(),
            password_policy: PasswordPolicy::default(),
            snapshot_interval: Some(300), // 5分钟
            timeout_ms: 30000, // 30秒
            retry_strategy: RetryStrategy::default(),
            auto_save: true,
            enable_compression: true,
            max_memory_mb: Some(64),
            debug_logging: true, // 启用调试日志以便排查问题
        }
    }
}

/// 适配器配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdapterConfig {
    /// 主后端类型
    pub primary_backend: BackendType,
    
    /// 是否启用 Stronghold
    pub enable_stronghold: bool,
    
    /// 是否启用 Keychain
    pub enable_keychain: bool,
    
    /// 故障转移策略
    pub failover_strategy: FailoverStrategy,
    
    /// Stronghold 配置
    pub stronghold_config: Option<StrongholdConfig>,
    
    /// 健康检查间隔
    pub health_check_interval: std::time::Duration,
    
    /// 最大重试次数
    pub max_retry_attempts: u32,
    
    /// 重试延迟
    pub retry_delay: std::time::Duration,
}

/// 健康检查配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    /// 健康检查间隔（秒）
    pub interval_seconds: u64,
    
    /// 健康检查超时（毫秒）
    pub timeout_ms: u64,
    
    /// 连续失败次数阈值
    pub failure_threshold: u32,
    
    /// 恢复检查间隔（秒）
    pub recovery_interval_seconds: u64,
    
    /// 是否启用健康检查
    pub enabled: bool,
}

impl Default for HealthCheckConfig {
    fn default() -> Self {
        Self {
            interval_seconds: 60,
            timeout_ms: 5000,
            failure_threshold: 3,
            recovery_interval_seconds: 30,
            enabled: true,
        }
    }
}

impl Default for AdapterConfig {
    fn default() -> Self {
        Self {
            primary_backend: BackendType::Stronghold,
            enable_stronghold: true,
            enable_keychain: true,
            failover_strategy: FailoverStrategy::Automatic,
            stronghold_config: None,
            health_check_interval: std::time::Duration::from_secs(300),
            max_retry_attempts: 3,
            retry_delay: std::time::Duration::from_secs(1),
        }
    }
}

impl StrongholdConfig {
    /// 验证配置的有效性
    pub fn validate(&self) -> StrongholdResult<()> {
        // 验证路径
        if self.vault_path.to_string_lossy().is_empty() {
            return Err(StrongholdError::ConfigValidationFailed(
                "Vault 路径不能为空".to_string(),
            ));
        }
        
        // 验证客户端名称
        if self.client_name.is_empty() {
            return Err(StrongholdError::ConfigValidationFailed(
                "客户端名称不能为空".to_string(),
            ));
        }
        
        // 验证超时时间
        if self.timeout_ms == 0 {
            return Err(StrongholdError::ConfigValidationFailed(
                "超时时间必须大于0".to_string(),
            ));
        }
        
        // 验证密码策略
        self.password_policy.validate()?;
        
        // 验证内存限制
        if let Some(max_memory) = self.max_memory_mb {
            if max_memory == 0 {
                return Err(StrongholdError::ConfigValidationFailed(
                    "最大内存使用量必须大于0".to_string(),
                ));
            }
        }
        
        Ok(())
    }
    
    /// 获取超时时间作为 Duration
    pub fn timeout_duration(&self) -> Duration {
        Duration::from_millis(self.timeout_ms)
    }
    
    /// 获取快照间隔作为 Duration
    pub fn snapshot_duration(&self) -> Option<Duration> {
        self.snapshot_interval.map(Duration::from_secs)
    }
}

impl PasswordPolicy {
    /// 验证密码策略配置
    pub fn validate(&self) -> StrongholdResult<()> {
        if self.min_length == 0 {
            return Err(StrongholdError::ConfigValidationFailed(
                "最小密码长度必须大于0".to_string(),
            ));
        }
        
        if self.max_length < self.min_length {
            return Err(StrongholdError::ConfigValidationFailed(
                "最大密码长度不能小于最小密码长度".to_string(),
            ));
        }
        
        if self.max_length > 1024 {
            return Err(StrongholdError::ConfigValidationFailed(
                "最大密码长度不能超过1024".to_string(),
            ));
        }
        
        Ok(())
    }
    
    /// 验证密码是否符合策略
    pub fn validate_password(&self, password: &str) -> StrongholdResult<()> {
        if password.len() < self.min_length {
            return Err(StrongholdError::ConfigValidationFailed(
                format!("密码长度不能少于{}字符", self.min_length),
            ));
        }
        
        if password.len() > self.max_length {
            return Err(StrongholdError::ConfigValidationFailed(
                format!("密码长度不能超过{}字符", self.max_length),
            ));
        }
        
        if self.require_numbers && !password.chars().any(|c| c.is_ascii_digit()) {
            return Err(StrongholdError::ConfigValidationFailed(
                "密码必须包含数字".to_string(),
            ));
        }
        
        if self.require_uppercase && !password.chars().any(|c| c.is_ascii_uppercase()) {
            return Err(StrongholdError::ConfigValidationFailed(
                "密码必须包含大写字母".to_string(),
            ));
        }
        
        if self.require_lowercase && !password.chars().any(|c| c.is_ascii_lowercase()) {
            return Err(StrongholdError::ConfigValidationFailed(
                "密码必须包含小写字母".to_string(),
            ));
        }
        
        if self.require_special_chars && !password.chars().any(|c| !c.is_alphanumeric()) {
            return Err(StrongholdError::ConfigValidationFailed(
                "密码必须包含特殊字符".to_string(),
            ));
        }
        
        Ok(())
    }
}

impl AdapterConfig {
    /// 验证适配器配置
    pub fn validate(&self) -> StrongholdResult<()> {
        // 检查主后端是否可用
        if !self.primary_backend.is_available() {
            return Err(StrongholdError::ConfigValidationFailed(
                format!("主后端 {} 在当前平台不可用", self.primary_backend.name()),
            ));
        }
        
        // 检查是否至少启用了一个后端
        if !self.enable_stronghold && !self.enable_keychain {
            return Err(StrongholdError::ConfigValidationFailed(
                "必须至少启用一个后端".to_string(),
            ));
        }
        
        // 验证重试次数
        if self.max_retry_attempts == 0 {
            return Err(StrongholdError::ConfigValidationFailed(
                "最大重试次数必须大于0".to_string(),
            ));
        }
        
        // 验证健康检查间隔
        if self.health_check_interval.is_zero() {
            return Err(StrongholdError::ConfigValidationFailed(
                "健康检查间隔必须大于0".to_string(),
            ));
        }
        
        Ok(())
    }
    
    /// 获取所有可用的后端（按优先级排序）
    pub fn available_backends(&self) -> Vec<BackendType> {
        let mut backends = Vec::new();
        
        // 添加主后端
        if self.primary_backend.is_available() {
            backends.push(self.primary_backend);
        }
        
        // 添加其他启用的后端
        if self.enable_stronghold && self.primary_backend != BackendType::Stronghold {
            if BackendType::Stronghold.is_available() {
                backends.push(BackendType::Stronghold);
            }
        }
        
        if self.enable_keychain && self.primary_backend != BackendType::Keychain {
            if BackendType::Keychain.is_available() {
                backends.push(BackendType::Keychain);
            }
        }
        
        backends
    }
}

/// 创建默认配置
pub fn create_default_config() -> StrongholdConfig {
    StrongholdConfig::default()
}

/// 创建默认适配器配置
pub fn create_default_adapter_config() -> AdapterConfig {
    AdapterConfig::default()
}

/// 创建高安全性配置
pub fn create_high_security_config() -> StrongholdConfig {
    StrongholdConfig {
        password_policy: PasswordPolicy {
            min_length: 12,
            max_length: 256,
            require_numbers: true,
            require_special_chars: true,
            require_uppercase: true,
            require_lowercase: true,
            expiry_days: Some(90),
        },
        snapshot_interval: Some(60), // 1分钟
        timeout_ms: 60000, // 60秒
        retry_strategy: RetryStrategy::conservative(),
        auto_save: true,
        enable_compression: true,
        max_memory_mb: Some(128),
        debug_logging: true,
        ..StrongholdConfig::default()
    }
}

/// 创建高安全性适配器配置
pub fn create_high_security_adapter_config() -> AdapterConfig {
    AdapterConfig {
        failover_strategy: FailoverStrategy::Smart,
        health_check_interval: std::time::Duration::from_secs(30),
        max_retry_attempts: 5,
        retry_delay: std::time::Duration::from_millis(500),
        ..AdapterConfig::default()
    }
}

/// 创建快速配置（适用于开发环境）
pub fn create_fast_config() -> StrongholdConfig {
    StrongholdConfig {
        password_policy: PasswordPolicy {
            min_length: 6,
            max_length: 64,
            require_numbers: false,
            require_special_chars: false,
            require_uppercase: false,
            require_lowercase: false,
            expiry_days: None,
        },
        snapshot_interval: Some(600), // 10分钟
        timeout_ms: 10000, // 10秒
        retry_strategy: RetryStrategy::fast(),
        auto_save: true,
        enable_compression: false,
        max_memory_mb: Some(32),
        debug_logging: true,
        ..StrongholdConfig::default()
    }
}

/// 创建快速适配器配置（适用于开发环境）
pub fn create_fast_adapter_config() -> AdapterConfig {
    AdapterConfig {
        failover_strategy: FailoverStrategy::Automatic,
        health_check_interval: std::time::Duration::from_secs(120),
        max_retry_attempts: 2,
        retry_delay: std::time::Duration::from_millis(100),
        ..AdapterConfig::default()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_backend_availability() {
        assert!(BackendType::Stronghold.is_available());
        
        // Keychain 可用性取决于平台
        #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
        assert!(BackendType::Keychain.is_available());
        
        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        assert!(!BackendType::Keychain.is_available());
    }

    #[test]
    fn test_stronghold_config_validation() {
        let mut config = StrongholdConfig::default();
        assert!(config.validate().is_ok());
        
        // 测试无效配置
        config.client_name = String::new();
        assert!(config.validate().is_err());
        
        config.client_name = "test".to_string();
        config.timeout_ms = 0;
        assert!(config.validate().is_err());
    }

    #[test]
    fn test_password_policy_validation() {
        let policy = PasswordPolicy::default();
        
        // 测试有效密码
        assert!(policy.validate_password("Password123!").is_ok());
        
        // 测试无效密码
        assert!(policy.validate_password("short").is_err()); // 太短
        assert!(policy.validate_password("nouppercase123!").is_err()); // 没有大写字母
        assert!(policy.validate_password("NOLOWERCASE123!").is_err()); // 没有小写字母
        assert!(policy.validate_password("NoNumbers!").is_err()); // 没有数字
        assert!(policy.validate_password("NoSpecialChars123").is_err()); // 没有特殊字符
    }

    #[test]
    fn test_adapter_config_validation() {
        let config = AdapterConfig::default();
        assert!(config.validate().is_ok());
        
        let available = config.available_backends();
        assert!(!available.is_empty());
        assert_eq!(available[0], BackendType::Stronghold);
    }

    #[test]
    fn test_config_presets() {
        let stronghold = create_default_config();
        assert!(stronghold.validate().is_ok());
        
        let adapter = create_default_adapter_config();
        assert!(adapter.validate().is_ok());
        
        let stronghold = create_high_security_config();
        assert!(stronghold.validate().is_ok());
        assert_eq!(stronghold.password_policy.min_length, 12);
        
        let adapter = create_high_security_adapter_config();
        assert!(adapter.validate().is_ok());
        
        let stronghold = create_fast_config();
        assert!(stronghold.validate().is_ok());
        assert_eq!(stronghold.password_policy.min_length, 6);
        
        let adapter = create_fast_adapter_config();
        assert!(adapter.validate().is_ok());
    }
} 