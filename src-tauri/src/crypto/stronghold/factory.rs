use std::sync::Arc;
use std::collections::HashMap;
use tokio::sync::RwLock;
use serde::{Deserialize, Serialize};
use std::time::Duration;

use super::{
    StrongholdAdapter, UnifiedStorageAdapter,
    StrongholdConfig, AdapterConfig, BackendType, FailoverStrategy,
    StrongholdError, StrongholdResult
};

/// 工厂配置选项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FactoryOptions {
    /// 是否启用缓存
    pub enable_cache: bool,
    /// 缓存大小限制
    pub cache_size_limit: usize,
    /// 是否启用预热
    pub enable_warmup: bool,
    /// 预热超时时间（秒）
    pub warmup_timeout_secs: u64,
    /// 是否启用健康检查
    pub enable_health_check: bool,
    /// 健康检查间隔（秒）
    pub health_check_interval_secs: u64,
    /// 是否启用高安全性配置
    pub enable_high_security: bool,
    /// 是否启用快速配置
    pub enable_fast: bool,
    /// 是否启用仅 Keychain 适配器
    pub enable_keychain_only: bool,
    /// 是否启用带故障转移的适配器
    pub enable_failover: bool,
    /// 主后端类型
    pub primary_backend: BackendType,
    /// 故障转移策略
    pub failover_strategy: FailoverStrategy,
    /// 高安全性配置
    pub high_security_config: Option<StrongholdConfig>,
    /// 快速配置
    pub fast_config: Option<StrongholdConfig>,
    /// 适配器配置
    pub adapter_config: Option<AdapterConfig>,
    /// 是否启用高安全性模式
    pub enable_high_security_mode: bool,
    /// 是否启用高安全性模式
    pub enable_caching: bool,
    /// 缓存大小
    pub cache_size: usize,
    /// 是否启用预预热
    pub enable_prewarming: bool,
    /// 超时时间
    pub timeout: Duration,
}

impl Default for FactoryOptions {
    fn default() -> Self {
        Self {
            enable_cache: true,
            cache_size_limit: 100,
            enable_warmup: false,
            warmup_timeout_secs: 30,
            enable_health_check: true,
            health_check_interval_secs: 300, // 5分钟
            enable_high_security: false,
            enable_fast: false,
            enable_keychain_only: false,
            enable_failover: false,
            primary_backend: BackendType::Stronghold,
            failover_strategy: FailoverStrategy::Automatic,
            high_security_config: None,
            fast_config: None,
            adapter_config: None,
            enable_high_security_mode: false,
            enable_caching: true,
            cache_size: 100,
            enable_prewarming: false,
            timeout: Duration::from_secs(30),
        }
    }
}

/// 实例缓存条目
#[derive(Debug)]
struct CacheEntry {
    /// 适配器实例
    adapter: Arc<UnifiedStorageAdapter>,
    /// 创建时间
    created_at: std::time::Instant,
    /// 最后访问时间
    last_accessed: std::time::Instant,
    /// 访问计数
    access_count: u64,
}

/// Stronghold 工厂类
/// 
/// 提供简化的实例创建、配置管理和缓存功能
#[derive(Debug)]
pub struct StrongholdFactory {
    /// 工厂选项
    options: FactoryOptions,
    /// 实例缓存
    cache: Arc<RwLock<HashMap<String, CacheEntry>>>,
    /// 默认配置
    default_config: AdapterConfig,
}

impl StrongholdFactory {
    /// 创建新的工厂实例
    /// 
    /// # 参数
    /// * `options` - 工厂选项
    /// 
    /// # 返回
    /// * `Self` - 工厂实例
    pub fn new(options: FactoryOptions) -> Self {
        Self {
            options,
            cache: Arc::new(RwLock::new(HashMap::new())),
            default_config: Self::create_default_adapter_config(),
        }
    }

    /// 使用默认选项创建工厂
    pub fn default() -> Self {
        Self::new(FactoryOptions::default())
    }

    /// 创建默认适配器配置
    fn create_default_adapter_config() -> AdapterConfig {
        AdapterConfig {
            primary_backend: BackendType::Stronghold,
            enable_stronghold: true,
            enable_keychain: true,
            failover_strategy: FailoverStrategy::Automatic,
            stronghold_config: Some(crate::crypto::stronghold::config::create_default_config()),
            health_check_interval: std::time::Duration::from_secs(300),
            max_retry_attempts: 3,
            retry_delay: std::time::Duration::from_secs(1),
        }
    }

    /// 创建统一存储适配器
    /// 
    /// # 参数
    /// * `config` - 可选的适配器配置
    /// 
    /// # 返回
    /// * `StrongholdResult<Arc<UnifiedStorageAdapter>>` - 适配器实例或错误
    pub async fn create_unified_adapter(
        &self,
        config: Option<AdapterConfig>
    ) -> StrongholdResult<Arc<UnifiedStorageAdapter>> {
        let config = config.unwrap_or_else(|| self.default_config.clone());
        let cache_key = self.generate_cache_key(&config);

        // 检查缓存
        if self.options.enable_cache {
            if let Some(cached) = self.get_from_cache(&cache_key).await {
                return Ok(cached);
            }
        }

        // 创建新实例
        let adapter = UnifiedStorageAdapter::new(config.clone()).await?;
        let adapter_arc = Arc::new(adapter);

        // 预热实例
        if self.options.enable_warmup {
            self.warmup_adapter(&adapter_arc).await?;
        }

        // 缓存实例
        if self.options.enable_cache {
            self.cache_adapter(&cache_key, Arc::clone(&adapter_arc)).await;
        }

        Ok(adapter_arc)
    }

    /// 创建 Stronghold 专用适配器
    /// 
    /// # 参数
    /// * `config` - 可选的 Stronghold 配置
    /// 
    /// # 返回
    /// * `StrongholdResult<Arc<StrongholdAdapter>>` - 适配器实例或错误
    pub async fn create_stronghold_adapter(
        &self,
        config: Option<StrongholdConfig>
    ) -> StrongholdResult<Arc<StrongholdAdapter>> {
        let config = config.unwrap_or_else(|| 
            crate::crypto::stronghold::config::create_default_config()
        );

        let adapter = StrongholdAdapter::new(config).await?;
        let adapter_arc = Arc::new(adapter);

        // 预热实例
        if self.options.enable_warmup {
            self.warmup_stronghold_adapter(&adapter_arc).await?;
        }

        Ok(adapter_arc)
    }

    /// 创建高安全性配置的适配器
    /// 
    /// # 返回
    /// * `StrongholdResult<Arc<UnifiedStorageAdapter>>` - 适配器实例或错误
    pub async fn create_high_security_adapter(&self) -> StrongholdResult<Arc<UnifiedStorageAdapter>> {
        let stronghold_config = crate::crypto::stronghold::config::create_high_security_config();
        let adapter_config = AdapterConfig {
            primary_backend: BackendType::Stronghold,
            enable_stronghold: true,
            enable_keychain: false, // 高安全性模式只使用 Stronghold
            failover_strategy: FailoverStrategy::None,
            stronghold_config: Some(stronghold_config),
            health_check_interval: std::time::Duration::from_secs(60), // 更频繁的健康检查
            max_retry_attempts: 5,
            retry_delay: std::time::Duration::from_millis(500),
        };

        self.create_unified_adapter(Some(adapter_config)).await
    }

    /// 创建快速配置的适配器
    /// 
    /// # 返回
    /// * `StrongholdResult<Arc<UnifiedStorageAdapter>>` - 适配器实例或错误
    pub async fn create_fast_adapter(&self) -> StrongholdResult<Arc<UnifiedStorageAdapter>> {
        let stronghold_config = crate::crypto::stronghold::config::create_fast_config();
        let adapter_config = AdapterConfig {
            primary_backend: BackendType::Stronghold,
            enable_stronghold: true,
            enable_keychain: true,
            failover_strategy: FailoverStrategy::Automatic,
            stronghold_config: Some(stronghold_config),
            health_check_interval: std::time::Duration::from_secs(600), // 较少的健康检查
            max_retry_attempts: 2,
            retry_delay: std::time::Duration::from_millis(100),
        };

        self.create_unified_adapter(Some(adapter_config)).await
    }

    /// 创建仅 Keychain 的适配器
    /// 
    /// # 返回
    /// * `StrongholdResult<Arc<UnifiedStorageAdapter>>` - 适配器实例或错误
    pub async fn create_keychain_only_adapter(&self) -> StrongholdResult<Arc<UnifiedStorageAdapter>> {
        let adapter_config = AdapterConfig {
            primary_backend: BackendType::Keychain,
            enable_stronghold: false,
            enable_keychain: true,
            failover_strategy: FailoverStrategy::None,
            stronghold_config: None,
            health_check_interval: std::time::Duration::from_secs(300),
            max_retry_attempts: 3,
            retry_delay: std::time::Duration::from_secs(1),
        };

        self.create_unified_adapter(Some(adapter_config)).await
    }

    /// 创建带故障转移的适配器
    /// 
    /// # 参数
    /// * `primary` - 主后端类型
    /// * `strategy` - 故障转移策略
    /// 
    /// # 返回
    /// * `StrongholdResult<Arc<UnifiedStorageAdapter>>` - 适配器实例或错误
    pub async fn create_failover_adapter(
        &self,
        primary: BackendType,
        strategy: FailoverStrategy
    ) -> StrongholdResult<Arc<UnifiedStorageAdapter>> {
        let adapter_config = AdapterConfig {
            primary_backend: primary,
            enable_stronghold: true,
            enable_keychain: true,
            failover_strategy: strategy,
            stronghold_config: Some(crate::crypto::stronghold::config::create_default_config()),
            health_check_interval: std::time::Duration::from_secs(120), // 更频繁的健康检查
            max_retry_attempts: 3,
            retry_delay: std::time::Duration::from_secs(1),
        };

        self.create_unified_adapter(Some(adapter_config)).await
    }

    /// 生成缓存键
    fn generate_cache_key(&self, config: &AdapterConfig) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        
        // 使用配置的关键字段生成哈希
        config.primary_backend.hash(&mut hasher);
        config.enable_stronghold.hash(&mut hasher);
        config.enable_keychain.hash(&mut hasher);
        config.failover_strategy.hash(&mut hasher);
        
        if let Some(stronghold_config) = &config.stronghold_config {
            stronghold_config.vault_path.hash(&mut hasher);
            stronghold_config.client_name.hash(&mut hasher);
        }

        format!("adapter_{:x}", hasher.finish())
    }

    /// 从缓存获取适配器
    async fn get_from_cache(&self, key: &str) -> Option<Arc<UnifiedStorageAdapter>> {
        let mut cache = self.cache.write().await;
        
        if let Some(entry) = cache.get_mut(key) {
            entry.last_accessed = std::time::Instant::now();
            entry.access_count += 1;
            Some(Arc::clone(&entry.adapter))
        } else {
            None
        }
    }

    /// 缓存适配器实例
    async fn cache_adapter(&self, key: &str, adapter: Arc<UnifiedStorageAdapter>) {
        let mut cache = self.cache.write().await;
        
        // 检查缓存大小限制
        if cache.len() >= self.options.cache_size_limit {
            self.evict_oldest_entry(&mut cache);
        }

        let entry = CacheEntry {
            adapter,
            created_at: std::time::Instant::now(),
            last_accessed: std::time::Instant::now(),
            access_count: 1,
        };

        cache.insert(key.to_string(), entry);
    }

    /// 驱逐最旧的缓存条目
    fn evict_oldest_entry(&self, cache: &mut HashMap<String, CacheEntry>) {
        if let Some((oldest_key, _)) = cache.iter()
            .min_by_key(|(_, entry)| entry.last_accessed) {
            let oldest_key = oldest_key.clone();
            cache.remove(&oldest_key);
            log::debug!("Evicted cache entry: {}", oldest_key);
        }
    }

    /// 预热适配器
    async fn warmup_adapter(&self, adapter: &Arc<UnifiedStorageAdapter>) -> StrongholdResult<()> {
        log::debug!("Warming up unified adapter");
        
        let timeout = std::time::Duration::from_secs(self.options.warmup_timeout_secs);
        
        // 使用 tokio::time::timeout 进行超时控制
        match tokio::time::timeout(timeout, self.perform_warmup_operations(adapter)).await {
            Ok(result) => result,
            Err(_) => {
                log::warn!("Adapter warmup timed out after {} seconds", self.options.warmup_timeout_secs);
                Err(StrongholdError::TimeoutError("Warmup timeout".to_string()))
            }
        }
    }

    /// 预热 Stronghold 适配器
    async fn warmup_stronghold_adapter(&self, adapter: &Arc<StrongholdAdapter>) -> StrongholdResult<()> {
        log::debug!("Warming up stronghold adapter");
        
        let timeout = std::time::Duration::from_secs(self.options.warmup_timeout_secs);
        
        match tokio::time::timeout(timeout, self.perform_stronghold_warmup_operations(adapter)).await {
            Ok(result) => result,
            Err(_) => {
                log::warn!("Stronghold adapter warmup timed out after {} seconds", self.options.warmup_timeout_secs);
                Err(StrongholdError::TimeoutError("Warmup timeout".to_string()))
            }
        }
    }

    /// 执行预热操作
    async fn perform_warmup_operations(&self, adapter: &Arc<UnifiedStorageAdapter>) -> StrongholdResult<()> {
        // 执行基本的健康检查操作
        let test_key = "warmup_test_key";
        let test_value = b"warmup_test_value";

        // 尝试存储测试数据
        adapter.store(test_key, test_value).await?;
        
        // 尝试读取测试数据
        let retrieved = adapter.get(test_key).await?;
        if retrieved.as_deref() != Some(test_value) {
            return Err(StrongholdError::OperationFailed(
                "Warmup data verification failed".to_string()
            ));
        }

        // 清理测试数据
        adapter.remove(test_key).await?;

        log::debug!("Unified adapter warmup completed successfully");
        Ok(())
    }

    /// 执行 Stronghold 预热操作
    async fn perform_stronghold_warmup_operations(&self, adapter: &Arc<StrongholdAdapter>) -> StrongholdResult<()> {
        // 执行基本的健康检查操作
        let test_key = "stronghold_warmup_test_key";
        let test_value = b"stronghold_warmup_test_value";

        // 尝试存储测试数据
        adapter.store(test_key, test_value).await?;
        
        // 尝试读取测试数据
        let retrieved = adapter.get(test_key).await?;
        if retrieved.as_deref() != Some(test_value) {
            return Err(StrongholdError::OperationFailed(
                "Stronghold warmup data verification failed".to_string()
            ));
        }

        // 清理测试数据
        adapter.remove(test_key).await?;

        log::debug!("Stronghold adapter warmup completed successfully");
        Ok(())
    }

    /// 清理缓存
    /// 
    /// # 参数
    /// * `max_age_secs` - 最大缓存时间（秒）
    pub async fn cleanup_cache(&self, max_age_secs: u64) {
        let mut cache = self.cache.write().await;
        let max_age = std::time::Duration::from_secs(max_age_secs);
        let now = std::time::Instant::now();

        cache.retain(|key, entry| {
            let age = now.duration_since(entry.created_at);
            let should_keep = age < max_age;
            
            if !should_keep {
                log::debug!("Removing expired cache entry: {}", key);
            }
            
            should_keep
        });
    }

    /// 获取缓存统计信息
    pub async fn get_cache_stats(&self) -> CacheStats {
        let cache = self.cache.read().await;
        
        let total_entries = cache.len();
        let total_access_count: u64 = cache.values().map(|entry| entry.access_count).sum();
        let avg_access_count = if total_entries > 0 {
            total_access_count as f64 / total_entries as f64
        } else {
            0.0
        };

        CacheStats {
            total_entries,
            total_access_count,
            avg_access_count,
            cache_size_limit: self.options.cache_size_limit,
        }
    }

    /// 清空缓存
    pub async fn clear_cache(&self) {
        let mut cache = self.cache.write().await;
        cache.clear();
        log::info!("Cache cleared");
    }

    /// 检查适配器能力
    /// 
    /// # 返回
    /// * `AdapterCapabilities` - 适配器能力信息
    pub async fn check_capabilities(&self) -> AdapterCapabilities {
        let mut capabilities = AdapterCapabilities::default();

        // 检查 Stronghold 可用性
        if let Ok(_) = self.create_stronghold_adapter(None).await {
            capabilities.stronghold_available = true;
        }

        // 检查 Keychain 可用性（通过创建仅 Keychain 适配器）
        if let Ok(_) = self.create_keychain_only_adapter().await {
            capabilities.keychain_available = true;
        }

        // 检查故障转移支持
        capabilities.failover_supported = capabilities.stronghold_available && capabilities.keychain_available;

        // 更新支持的后端列表
        capabilities.update_supported_backends();

        capabilities
    }

    /// 获取工厂选项
    pub fn get_options(&self) -> &FactoryOptions {
        &self.options
    }

    /// 更新工厂选项
    pub fn update_options(&mut self, options: FactoryOptions) {
        self.options = options;
    }

    /// 创建默认配置的工厂实例
    pub fn with_default_config() -> Self {
        Self {
            options: FactoryOptions {
                high_security_config: Some(crate::crypto::stronghold::config::create_default_config()),
                adapter_config: Some(crate::crypto::stronghold::config::create_default_adapter_config()),
                ..FactoryOptions::default()
            },
            cache: Arc::new(RwLock::new(HashMap::new())),
            default_config: Self::create_default_adapter_config(),
        }
    }

    /// 创建高安全性配置的工厂实例
    pub fn with_high_security_config() -> Self {
        Self {
            options: FactoryOptions {
                high_security_config: Some(crate::crypto::stronghold::config::create_high_security_config()),
                adapter_config: Some(crate::crypto::stronghold::config::create_high_security_adapter_config()),
                enable_caching: false, // 高安全性模式禁用缓存
                ..FactoryOptions::default()
            },
            cache: Arc::new(RwLock::new(HashMap::new())),
            default_config: Self::create_default_adapter_config(),
        }
    }

    /// 创建快速配置的工厂实例（适用于开发环境）
    pub fn with_fast_config() -> Self {
        Self {
            options: FactoryOptions {
                fast_config: Some(crate::crypto::stronghold::config::create_fast_config()),
                adapter_config: Some(crate::crypto::stronghold::config::create_fast_adapter_config()),
                enable_prewarming: true,
                cache_size: 200,
                ..FactoryOptions::default()
            },
            cache: Arc::new(RwLock::new(HashMap::new())),
            default_config: Self::create_default_adapter_config(),
        }
    }

    /// 创建默认适配器
    pub async fn create_default_adapter(&self) -> StrongholdResult<UnifiedStorageAdapter> {
        let config = self.options.adapter_config.clone()
            .unwrap_or_else(|| crate::crypto::stronghold::config::create_default_adapter_config());
        
        UnifiedStorageAdapter::new(config).await
    }
}

/// 缓存统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheStats {
    /// 总条目数
    pub total_entries: usize,
    /// 总访问次数
    pub total_access_count: u64,
    /// 平均访问次数
    pub avg_access_count: f64,
    /// 缓存大小限制
    pub cache_size_limit: usize,
}

/// 适配器能力信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AdapterCapabilities {
    /// Stronghold 是否可用
    pub stronghold_available: bool,
    /// Keychain 是否可用
    pub keychain_available: bool,
    /// 是否支持故障转移
    pub failover_supported: bool,
    /// 支持的后端类型
    pub supported_backends: Vec<BackendType>,
}

impl Default for AdapterCapabilities {
    fn default() -> Self {
        Self {
            stronghold_available: false,
            keychain_available: false,
            failover_supported: false,
            supported_backends: Vec::new(),
        }
    }
}

impl AdapterCapabilities {
    /// 更新支持的后端列表
    pub fn update_supported_backends(&mut self) {
        self.supported_backends.clear();
        
        if self.stronghold_available {
            self.supported_backends.push(BackendType::Stronghold);
        }
        
        if self.keychain_available {
            self.supported_backends.push(BackendType::Keychain);
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_factory_creation() {
        let factory = StrongholdFactory::default();
        assert!(factory.options.enable_cache);
        assert_eq!(factory.options.cache_size_limit, 100);
    }

    #[tokio::test]
    async fn test_factory_options() {
        let options = FactoryOptions {
            enable_cache: false,
            cache_size_limit: 50,
            enable_warmup: false,
            warmup_timeout_secs: 10,
            enable_health_check: false,
            health_check_interval_secs: 60,
            enable_high_security: false,
            enable_fast: false,
            enable_keychain_only: false,
            enable_failover: false,
            primary_backend: BackendType::Stronghold,
            failover_strategy: FailoverStrategy::Automatic,
            high_security_config: None,
            fast_config: None,
            adapter_config: None,
            enable_high_security_mode: false,
            enable_caching: true,
            cache_size: 100,
            enable_prewarming: false,
            timeout: Duration::from_secs(30),
        };

        let factory = StrongholdFactory::new(options.clone());
        assert_eq!(factory.options.enable_cache, options.enable_cache);
        assert_eq!(factory.options.cache_size_limit, options.cache_size_limit);
    }

    #[tokio::test]
    async fn test_create_stronghold_adapter() {
        let factory = StrongholdFactory::default();
        let result = factory.create_stronghold_adapter(None).await;
        
        // 这个测试可能会失败，如果 Stronghold 不可用
        // 在实际环境中应该根据具体情况调整
        match result {
            Ok(_) => {
                // 适配器创建成功
            }
            Err(e) => {
                // 适配器创建失败，可能是因为环境问题
                println!("Stronghold adapter creation failed: {}", e);
            }
        }
    }

    #[tokio::test]
    async fn test_cache_key_generation() {
        let factory = StrongholdFactory::default();
        let config1 = factory.default_config.clone();
        let mut config2 = factory.default_config.clone();
        config2.primary_backend = BackendType::Keychain;

        let key1 = factory.generate_cache_key(&config1);
        let key2 = factory.generate_cache_key(&config2);

        assert_ne!(key1, key2);
        assert!(key1.starts_with("adapter_"));
        assert!(key2.starts_with("adapter_"));
    }

    #[tokio::test]
    async fn test_cache_stats() {
        let factory = StrongholdFactory::default();
        let stats = factory.get_cache_stats().await;
        
        assert_eq!(stats.total_entries, 0);
        assert_eq!(stats.total_access_count, 0);
        assert_eq!(stats.avg_access_count, 0.0);
        assert_eq!(stats.cache_size_limit, 100);
    }

    #[tokio::test]
    async fn test_adapter_capabilities() {
        let mut capabilities = AdapterCapabilities::default();
        assert!(!capabilities.stronghold_available);
        assert!(!capabilities.keychain_available);
        assert!(!capabilities.failover_supported);
        assert!(capabilities.supported_backends.is_empty());

        capabilities.stronghold_available = true;
        capabilities.keychain_available = true;
        capabilities.failover_supported = true;
        capabilities.update_supported_backends();

        assert_eq!(capabilities.supported_backends.len(), 2);
        assert!(capabilities.supported_backends.contains(&BackendType::Stronghold));
        assert!(capabilities.supported_backends.contains(&BackendType::Keychain));
    }

    #[tokio::test]
    async fn test_cache_cleanup() {
        let factory = StrongholdFactory::default();
        
        // 清理缓存（即使是空的）
        factory.cleanup_cache(3600).await; // 1小时
        
        let stats = factory.get_cache_stats().await;
        assert_eq!(stats.total_entries, 0);
    }

    #[tokio::test]
    async fn test_clear_cache() {
        let factory = StrongholdFactory::default();
        
        // 清空缓存
        factory.clear_cache().await;
        
        let stats = factory.get_cache_stats().await;
        assert_eq!(stats.total_entries, 0);
    }
} 