# Stronghold 后端动态管理

本文档介绍如何使用 UnifiedStorageAdapter 中新添加的后端管理方法来动态启用、禁用和检查 Stronghold 后端的一致性。

## 方法概述

### 1. `disable_stronghold_backend()`

当 Stronghold 初始化失败时调用此方法来禁用 Stronghold 后端。

**功能：**
- 设置 `self.config.enable_stronghold = false`
- 清理 `self.stronghold_manager = None`
- 更新健康检查状态为不可用
- 如果当前活跃后端是 Stronghold，自动切换到 Keychain
- 如果没有其他可用后端，返回 `NoBackendAvailable` 错误

**使用场景：**
- Stronghold 初始化失败时的错误处理
- 运行时检测到 Stronghold 不可用时的故障恢复
- 用户手动禁用 Stronghold 功能

### 2. `enable_stronghold_backend(manager)`

当 Stronghold 初始化成功时调用此方法来启用 Stronghold 后端。

**功能：**
- 设置 `self.stronghold_manager = Some(manager)`
- 确保 `self.config.enable_stronghold = true`
- 执行健康检查验证 Stronghold 可用性
- 根据策略重新选择最佳后端

**使用场景：**
- Stronghold 初始化成功后的启用
- 运行时恢复 Stronghold 功能
- 动态切换到 Stronghold 后端

### 3. `check_backend_consistency()`

检查后端配置与实际状态的一致性。

**功能：**
- 验证配置与实际管理器状态是否匹配
- 检查健康状态的一致性
- 验证活跃后端配置的正确性
- 检查故障转移策略的合理性
- 返回所有发现的问题列表

**使用场景：**
- 系统启动时的配置验证
- 定期健康检查
- 调试配置问题
- 运行时状态监控

## 使用示例

### 示例 1：处理 Stronghold 初始化失败

```rust
use crate::crypto::stronghold::{
    adapter::UnifiedStorageAdapter,
    config::create_fast_adapter_config,
    manager::StrongholdManager,
};

async fn initialize_storage() -> Result<UnifiedStorageAdapter, StrongholdError> {
    let config = create_fast_adapter_config();
    let mut adapter = UnifiedStorageAdapter::new(config).await?;
    
    // 尝试初始化 Stronghold
    match StrongholdManager::new(app_handle, stronghold_config).await {
        Ok(manager) => {
            // 初始化成功，启用 Stronghold 后端
            adapter.enable_stronghold_backend(manager).await?;
            log::info!("Stronghold 后端已成功启用");
        }
        Err(e) => {
            // 初始化失败，禁用 Stronghold 后端
            log::warn!("Stronghold 初始化失败: {}, 禁用后端", e);
            adapter.disable_stronghold_backend().await?;
        }
    }
    
    Ok(adapter)
}
```

### 示例 2：运行时后端切换

```rust
async fn handle_stronghold_recovery(
    adapter: &mut UnifiedStorageAdapter,
    app_handle: &AppHandle,
) -> Result<(), StrongholdError> {
    // 检查当前状态
    let issues = adapter.check_backend_consistency().await;
    
    if !issues.is_empty() {
        log::warn!("发现配置问题: {:?}", issues);
        
        // 尝试重新初始化 Stronghold
        if let Some(stronghold_config) = &adapter.config.stronghold_config {
            match StrongholdManager::new(app_handle, stronghold_config.clone()).await {
                Ok(manager) => {
                    adapter.enable_stronghold_backend(manager).await?;
                    log::info!("Stronghold 后端已恢复");
                }
                Err(e) => {
                    log::error!("Stronghold 恢复失败: {}", e);
                    adapter.disable_stronghold_backend().await?;
                }
            }
        }
    }
    
    Ok(())
}
```

### 示例 3：定期健康检查

```rust
use tokio::time::{interval, Duration};

async fn periodic_health_check(adapter: Arc<RwLock<UnifiedStorageAdapter>>) {
    let mut interval = interval(Duration::from_secs(300)); // 每5分钟检查一次
    
    loop {
        interval.tick().await;
        
        let adapter_read = adapter.read().await;
        let issues = adapter_read.check_backend_consistency().await;
        
        if !issues.is_empty() {
            log::warn!("健康检查发现问题:");
            for issue in &issues {
                log::warn!("  - {}", issue);
            }
            
            // 可以在这里触发告警或自动修复逻辑
            // 例如：发送通知、尝试重新初始化等
        } else {
            log::debug!("健康检查通过");
        }
    }
}
```

### 示例 4：故障转移处理

```rust
async fn handle_storage_operation(
    adapter: &mut UnifiedStorageAdapter,
    key: &str,
    value: &[u8],
) -> Result<(), StrongholdError> {
    // 尝试存储操作
    match adapter.store(key, value).await {
        Ok(_) => {
            log::debug!("存储操作成功");
            Ok(())
        }
        Err(e) => {
            log::warn!("存储操作失败: {}", e);
            
            // 检查是否需要禁用当前后端
            let active_backend = adapter.get_active_backend().await;
            if matches!(active_backend, BackendType::Stronghold) {
                if let Err(disable_err) = adapter.disable_stronghold_backend().await {
                    log::error!("禁用 Stronghold 后端失败: {}", disable_err);
                    return Err(disable_err);
                }
                
                // 重试操作
                adapter.store(key, value).await
            } else {
                Err(e)
            }
        }
    }
}
```

## 最佳实践

### 1. 错误处理

- 始终检查 `disable_stronghold_backend()` 的返回值
- 如果返回 `NoBackendAvailable` 错误，说明系统没有可用的存储后端
- 在这种情况下，应该通知用户或触发紧急处理流程

### 2. 状态监控

- 定期调用 `check_backend_consistency()` 进行状态监控
- 建议在系统启动、配置更改和定期健康检查时调用
- 将发现的问题记录到日志中，便于调试和监控

### 3. 性能考虑

- `check_backend_consistency()` 会执行实际的健康检查，可能有一定开销
- 不要在高频操作中调用此方法
- 建议使用后台任务进行定期检查

### 4. 线程安全

- 这些方法都是异步的，可以安全地在多线程环境中使用
- 内部使用了 `RwLock` 和 `Arc` 确保线程安全
- 在并发环境中使用时，注意避免死锁

## 注意事项

1. **AppHandle 依赖**：`enable_stronghold_backend()` 需要一个已初始化的 `StrongholdManager`，而创建 `StrongholdManager` 需要 `AppHandle`。

2. **配置一致性**：这些方法会修改内部配置状态，确保在多个地方使用同一个适配器实例。

3. **错误传播**：适当处理这些方法返回的错误，特别是 `NoBackendAvailable` 错误。

4. **日志记录**：这些方法会生成详细的日志信息，确保配置了适当的日志级别。

通过合理使用这些方法，您可以构建一个健壮的、能够动态适应不同环境的存储系统。 