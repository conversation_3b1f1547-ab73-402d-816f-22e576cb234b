# Stronghold 安全存储集成指南

## 概述

本指南详细说明了如何在 Tauri 2.5.0 应用中集成和使用 Stronghold 安全存储系统。Stronghold 基于 IOTA 的军用级加密技术，提供硬件级安全保护。

## 架构概览

```
┌─────────────────────────────────────────────────────────────┐
│                    前端 (React/TypeScript)                    │
├─────────────────────────────────────────────────────────────┤
│                    Tauri 命令接口                           │
├─────────────────────────────────────────────────────────────┤
│                  Stronghold 服务层                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   工厂模式      │  │   适配器模式    │  │   缓存系统   │ │
│  │ StrongholdFactory│  │UnifiedAdapter   │  │  LRU Cache   │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    后端存储层                               │
│  ┌─────────────────┐           ┌─────────────────┐          │
│  │   Stronghold    │           │    Keychain     │          │
│  │  (主要后端)     │  <──────> │   (备用后端)    │          │
│  └─────────────────┘           └─────────────────┘          │
└─────────────────────────────────────────────────────────────┘
```

## 核心特性

### 🔒 安全特性
- **军用级加密**: 基于 IOTA Stronghold 的硬件级安全
- **多层防护**: 密码哈希 + 硬件安全模块 + 内存保护
- **零知识架构**: 密钥永不以明文形式存储

### 🔄 高可用性
- **双后端架构**: Stronghold + Keychain 故障转移
- **自动恢复**: 后端故障时自动切换
- **健康监控**: 实时监控后端状态

### ⚡ 性能优化
- **LRU 缓存**: 减少重复加密/解密操作
- **异步非阻塞**: 全异步 API 设计
- **批量操作**: 支持批量数据处理

### 🛠️ 开发友好
- **TypeScript 支持**: 完整的类型定义
- **配置预设**: 默认、高安全性、快速三种模式
- **详细日志**: 完整的操作日志和错误追踪

## 快速开始

### 1. 后端初始化

Stronghold 插件已在 `lib.rs` 中自动初始化：

```rust
// src-tauri/src/lib.rs
.plugin(tauri_plugin_stronghold::Builder::new(stronghold_password_provider).build())
```

### 2. 前端使用

```typescript
import { strongholdService, createDefaultInitParams } from '@/lib/stronghold';

// 初始化服务
const initParams = createDefaultInitParams('your_secure_password');
const status = await strongholdService.initialize(initParams);

// 存储数据
await strongholdService.store('user_token', 'secret_token_value');

// 获取数据
const token = await strongholdService.getString('user_token');

// 删除数据
await strongholdService.remove('user_token');
```

### 3. React 组件示例

```tsx
import React, { useEffect, useState } from 'react';
import { strongholdService } from '@/lib/stronghold';

export const SecureStorage: React.FC = () => {
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    const initStronghold = async () => {
      try {
        const status = await strongholdService.getStatus();
        setInitialized(status.initialized);
      } catch (error) {
        console.error('检查 Stronghold 状态失败:', error);
      }
    };

    initStronghold();
  }, []);

  const handleStore = async (key: string, value: string) => {
    try {
      await strongholdService.store(key, value);
      console.log('数据存储成功');
    } catch (error) {
      console.error('存储失败:', error);
    }
  };

  return (
    <div>
      <h2>安全存储 {initialized ? '✅' : '❌'}</h2>
      {/* 你的 UI 组件 */}
    </div>
  );
};
```

## 配置选项

### 预设配置

#### 1. 默认配置 (推荐)
```typescript
const params = createDefaultInitParams(password);
// - 平衡安全性和性能
// - 启用故障转移
// - 适合大多数应用场景
```

#### 2. 高安全性配置
```typescript
const params = createHighSecurityInitParams(password);
// - 最高安全级别
// - 禁用故障转移
// - 适合敏感数据存储
```

#### 3. 快速配置 (开发环境)
```typescript
const params = createFastInitParams(password);
// - 优化性能
// - 启用故障转移
// - 适合开发和测试
```

### 自定义配置

```typescript
const customParams: StrongholdInitParams = {
  password: 'your_password',
  config_type: 'custom',
  custom_config: {
    // 自定义 Stronghold 配置
    cache_size: 1000,
    timeout_ms: 5000,
    retry_attempts: 3,
  },
  enable_failover: true,
  primary_backend: 'stronghold',
};
```

## API 参考

### 服务管理

#### `initialize(params: StrongholdInitParams)`
初始化 Stronghold 服务

```typescript
const status = await strongholdService.initialize({
  password: 'secure_password',
  config_type: 'default',
  enable_failover: true,
});
```

#### `getStatus(): Promise<StrongholdServiceStatus>`
获取服务状态

```typescript
const status = await strongholdService.getStatus();
console.log('初始化状态:', status.initialized);
console.log('活跃后端:', status.active_backend);
```

#### `reset(): Promise<void>`
重置服务（清除所有数据）

```typescript
await strongholdService.reset();
```

### 数据操作

#### `store(key: string, value: string | Uint8Array, itemType?: string)`
存储数据

```typescript
// 存储字符串
await strongholdService.store('api_key', 'sk-1234567890');

// 存储二进制数据
const binaryData = new Uint8Array([1, 2, 3, 4]);
await strongholdService.store('binary_data', binaryData, 'binary');
```

#### `get(key: string): Promise<Uint8Array | null>`
获取原始数据

```typescript
const data = await strongholdService.get('api_key');
if (data) {
  const text = new TextDecoder().decode(data);
  console.log('API Key:', text);
}
```

#### `getString(key: string): Promise<string | null>`
获取字符串数据

```typescript
const apiKey = await strongholdService.getString('api_key');
if (apiKey) {
  console.log('API Key:', apiKey);
}
```

#### `remove(key: string): Promise<boolean>`
删除数据

```typescript
const removed = await strongholdService.remove('api_key');
console.log('删除成功:', removed);
```

#### `exists(key: string): Promise<boolean>`
检查键是否存在

```typescript
const exists = await strongholdService.exists('api_key');
console.log('键存在:', exists);
```

#### `listKeys(): Promise<string[]>`
列出所有键

```typescript
const keys = await strongholdService.listKeys();
console.log('存储的键:', keys);
```

### 后端管理

#### `switchBackend(backend: 'stronghold' | 'keychain')`
切换后端

```typescript
await strongholdService.switchBackend('keychain');
```

#### `getBackendHealth(): Promise<BackendHealth[]>`
获取后端健康状态

```typescript
const health = await strongholdService.getBackendHealth();
health.forEach(backend => {
  console.log(`${backend.backend}: ${backend.status}`);
});
```

### 系统信息

#### `checkCapabilities(): Promise<StrongholdCapabilities>`
检查系统能力

```typescript
const capabilities = await strongholdService.checkCapabilities();
console.log('Stronghold 可用:', capabilities.stronghold_available);
console.log('Keychain 可用:', capabilities.keychain_available);
```

#### `getCacheStats(): Promise<CacheStats>`
获取缓存统计

```typescript
const stats = await strongholdService.getCacheStats();
console.log('缓存条目:', stats.total_entries);
console.log('命中率:', stats.avg_access_count);
```

#### `cleanupCache(maxAgeSecs?: number)`
清理缓存

```typescript
// 清理1小时前的缓存
await strongholdService.cleanupCache(3600);
```

## 最佳实践

### 1. 密码管理

```typescript
// ❌ 不要硬编码密码
const password = 'hardcoded_password';

// ✅ 从环境变量获取
const password = process.env.STRONGHOLD_PASSWORD || await getUserPassword();

// ✅ 使用安全的密码生成
import { generateSecurePassword } from '@/utils/crypto';
const password = generateSecurePassword(32);
```

### 2. 错误处理

```typescript
try {
  await strongholdService.store('key', 'value');
} catch (error) {
  if (error.message.includes('Backend not available')) {
    // 处理后端不可用
    await strongholdService.switchBackend('keychain');
    await strongholdService.store('key', 'value');
  } else {
    // 其他错误处理
    console.error('存储失败:', error);
    throw error;
  }
}
```

### 3. 生命周期管理

```typescript
// 应用启动时
useEffect(() => {
  const initializeStronghold = async () => {
    try {
      const capabilities = await strongholdService.checkCapabilities();
      if (capabilities.stronghold_available) {
        const params = createDefaultInitParams(await getPassword());
        await strongholdService.initialize(params);
      }
    } catch (error) {
      console.error('初始化失败:', error);
    }
  };

  initializeStronghold();
}, []);

// 应用关闭时
useEffect(() => {
  return () => {
    // 清理敏感数据
    strongholdService.cleanupCache(0);
  };
}, []);
```

### 4. 性能优化

```typescript
// ✅ 批量操作
const keys = ['key1', 'key2', 'key3'];
const values = await Promise.all(
  keys.map(key => strongholdService.getString(key))
);

// ✅ 缓存频繁访问的数据
const cache = new Map<string, string>();
const getCachedValue = async (key: string) => {
  if (cache.has(key)) {
    return cache.get(key);
  }
  const value = await strongholdService.getString(key);
  if (value) {
    cache.set(key, value);
  }
  return value;
};
```

## 故障排除

### 常见问题

#### 1. 初始化失败
```
Error: Stronghold initialization failed
```

**解决方案:**
- 检查密码是否正确
- 确认 Stronghold 插件已正确安装
- 查看系统能力: `checkCapabilities()`

#### 2. 后端不可用
```
Error: Backend not available
```

**解决方案:**
- 检查后端健康状态: `getBackendHealth()`
- 尝试切换后端: `switchBackend('keychain')`
- 重新初始化服务: `reset()` 然后 `initialize()`

#### 3. 权限错误
```
Error: Permission denied
```

**解决方案:**
- 确认应用有访问 Keychain 的权限 (macOS)
- 检查文件系统权限 (Linux/Windows)
- 以管理员权限运行 (Windows)

### 调试技巧

#### 1. 启用详细日志
```rust
// 在 main.rs 中设置日志级别
env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("debug")).init();
```

#### 2. 监控服务状态
```typescript
// 定期检查服务状态
setInterval(async () => {
  const status = await strongholdService.getStatus();
  console.log('服务状态:', status);
}, 30000);
```

#### 3. 健康检查
```typescript
const performHealthCheck = async () => {
  const [capabilities, health, stats] = await Promise.all([
    strongholdService.checkCapabilities(),
    strongholdService.getBackendHealth(),
    strongholdService.getCacheStats(),
  ]);

  console.log('系统能力:', capabilities);
  console.log('后端健康:', health);
  console.log('缓存统计:', stats);
};
```

## 安全考虑

### 1. 密码安全
- 使用强密码 (至少 16 字符)
- 定期更换密码
- 不要在日志中记录密码

### 2. 数据分类
- 敏感数据使用高安全性配置
- 临时数据使用快速配置
- 根据数据重要性选择合适的后端

### 3. 访问控制
- 实现适当的身份验证
- 使用最小权限原则
- 定期审计访问日志

### 4. 备份和恢复
- 定期备份 Stronghold 数据
- 测试恢复流程
- 准备灾难恢复计划

## 更新日志

### v1.0.0 (2024-12-19)
- ✅ 完整的 Stronghold 集成
- ✅ 双后端架构 (Stronghold + Keychain)
- ✅ 故障转移机制
- ✅ LRU 缓存系统
- ✅ TypeScript 接口
- ✅ React 演示组件
- ✅ 37 个单元测试 (100% 通过)
- ✅ 完整文档和示例

## 支持

如果遇到问题或需要帮助，请：

1. 查看本文档的故障排除部分
2. 检查项目的 GitHub Issues
3. 查看 Tauri 和 Stronghold 官方文档
4. 联系开发团队

---

**注意**: 本系统处理敏感数据，请确保在生产环境中遵循最佳安全实践。 