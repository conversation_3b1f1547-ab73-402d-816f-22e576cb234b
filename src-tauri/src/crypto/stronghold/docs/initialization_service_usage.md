# Stronghold 初始化服务使用指南

## 问题背景

在之前的实现中，`initialize_backends` 方法无法确保 `lib.rs` 中的 Stronghold 是否初始化完成，因为 `StrongholdManager::new()` 需要 `AppHandle`，而适配器创建时可能 `AppHandle` 还不可用。

## 解决方案概述

graph TD
    A[开始初始化] --> B[基础 Stronghold 初始化]
    B --> C{基础初始化成功?}
    C -->|是| D[执行增强检测]
    C -->|否| E[记录错误，继续应用初始化]
    
    D --> F{检测三个层次}
    F --> G[自定义服务状态]
    F --> H[Tauri 插件状态]  
    F --> I[读写功能测试]
    
    G --> J{综合评估}
    H --> J
    I --> J
    
    J -->|通过| K[✅ Stronghold 完全可用]
    J -->|未通过| L[等待最多30秒]
    
    L --> M{等待期间变可用?}
    M -->|是| K
    M -->|否| N[⚠️ 使用 Keychain 备选]
    
    K --> O[继续 ORM 初始化]
    N --> O
    E --> O

我们创建了一个全局的 `StrongholdInitializationService`，它负责：

1. **管理完整的初始化流程**：包括密码提供器、管理器和适配器的初始化
2. **处理 AppHandle 依赖**：等待 AppHandle 可用后再进行初始化
3. **提供状态跟踪**：让其他组件能够查询初始化状态
4. **支持重试机制**：在初始化失败时提供自动重试
5. **前端通知**：向前端发送初始化状态事件

## 核心组件

### 1. InitializationStatus 枚举

```rust
pub enum InitializationStatus {
    NotInitialized,        // 未初始化
    Initializing,          // 正在初始化
    Initialized,           // 初始化成功
    Failed(String),        // 初始化失败
    PasswordProviderNotReady, // 密码提供器未就绪
    WaitingForAppHandle,   // 等待 AppHandle
}
```

### 2. StrongholdInitializationService

全局单例服务，管理整个 Stronghold 的初始化生命周期：

```rust
pub struct StrongholdInitializationService {
    status: InitializationStatus,
    app_handle: Option<AppHandle>,
    adapter: Option<Arc<RwLock<UnifiedStorageAdapter>>>,
    stronghold_manager: Option<Arc<RwLock<StrongholdManager>>>,
    // ... 其他字段
}
```

## 初始化流程

### 1. lib.rs 中的初始化顺序

```rust
// 步骤 1: 首先初始化 Stronghold 服务
crypto::stronghold::initialize_global_stronghold_service(app_handle.clone()).await

// 步骤 2: 初始化 Token 管理器
auth::token_manager::initialize_global_token_manager(app_handle.clone()).await

// 步骤 3: 初始化 ORM 服务（此时 Stronghold 已准备就绪）
initialize_orm_service(&app_handle, &app_state).await
```

### 2. 完整的 Stronghold 初始化流程

1. **检查 AppHandle 可用性**
2. **初始化密码提供器**
3. **验证密码可用性**
4. **创建 Stronghold 管理器**
5. **创建统一存储适配器**
6. **启用 Stronghold 后端**
7. **执行一致性检查**
8. **保存适配器实例**

### 3. adapter.rs 中的智能等待机制

```rust
match initial_status {
    InitializationStatus::Initialized => {
        // 直接获取已初始化的管理器
    }
    InitializationStatus::Initializing => {
        // 等待初始化完成（最多 10 秒）
        wait_for_global_stronghold_initialization(10).await
    }
    InitializationStatus::WaitingForAppHandle => {
        // 等待 AppHandle 可用（最多 5 秒）
        wait_for_global_stronghold_initialization(5).await
    }
    // ... 其他状态处理
}
```

## 主要 API 函数

### 全局服务管理

```rust
// 初始化全局服务
pub async fn initialize_global_stronghold_service(app_handle: AppHandle) -> StrongholdResult<()>

// 等待初始化完成
pub async fn wait_for_global_stronghold_initialization(timeout_seconds: u64) -> StrongholdResult<()>

// 获取统一存储适配器
pub async fn get_global_unified_adapter() -> Option<Arc<RwLock<UnifiedStorageAdapter>>>

// 获取 Stronghold 管理器
pub async fn get_global_stronghold_manager() -> Option<Arc<RwLock<StrongholdManager>>>

// 检查初始化状态
pub async fn get_global_stronghold_status() -> InitializationStatus

// 重试初始化
pub async fn retry_global_stronghold_initialization() -> StrongholdResult<()>

// 重置服务
pub async fn reset_global_stronghold_service() -> StrongholdResult<()>
```

## 前端事件

服务会向前端发送以下事件：

### 1. stronghold_initialization_status
```json
{
    "status": "Initializing",
    "message": "正在初始化 Stronghold 服务...",
    "attempts": 1,
    "max_attempts": 3,
    "can_retry": true,
    "is_production": false
}
```

### 2. stronghold_initialization_error
```json
{
    "error": "初始化失败的具体原因",
    "attempts": 1,
    "max_attempts": 3,
    "can_retry": true
}
```

### 3. stronghold_service_ready
```json
{
    "status": "initialized",
    "message": "Stronghold 服务已完全初始化并可用"
}
```

## 错误处理

### 1. 自动重试机制
- 最多重试 3 次
- 每次重试之间有延迟
- 重试失败后会保持错误状态

### 2. 优雅降级
- 如果 Stronghold 初始化失败，系统会自动切换到 Keychain 作为备选方案
- 应用仍能正常运行，只是安全级别稍低

### 3. 超时处理
- 等待初始化时有合理的超时时间
- 超时后会返回明确的错误信息

## 使用示例

### 1. 在应用启动时初始化

```rust
// 在 lib.rs 的 setup_common_features 中
match crypto::stronghold::initialize_global_stronghold_service(app_handle.clone()).await {
    Ok(()) => {
        log::info!("Stronghold 服务初始化成功");
        // 通知前端
    }
    Err(e) => {
        log::error!("Stronghold 服务初始化失败: {}", e);
        // 系统仍可正常运行，使用 Keychain 作为备选
    }
}
```

### 2. 在其他组件中检查状态

```rust
// 检查 Stronghold 是否可用
let status = crypto::stronghold::get_global_stronghold_status().await;
match status {
    InitializationStatus::Initialized => {
        // 可以安全使用 Stronghold
        if let Some(adapter) = crypto::stronghold::get_global_unified_adapter().await {
            // 使用适配器
        }
    }
    _ => {
        // Stronghold 不可用，使用备选方案
    }
}
```

### 3. 在前端监听事件

```javascript
// 监听初始化状态
window.__TAURI__.event.listen('stronghold_initialization_status', (event) => {
    console.log('Stronghold 初始化状态:', event.payload);
    // 更新 UI 状态
});

// 监听服务就绪事件
window.__TAURI__.event.listen('stronghold_service_ready', (event) => {
    console.log('Stronghold 服务已就绪:', event.payload);
    // 启用相关功能
});

// 监听错误事件
window.__TAURI__.event.listen('stronghold_initialization_error', (event) => {
    console.error('Stronghold 初始化错误:', event.payload);
    // 显示错误信息或降级提示
});
```

## 优势

1. **解决 AppHandle 依赖问题**：通过全局服务确保正确的初始化时序
2. **保证状态一致性**：`initialize_backends` 现在能确保与全局服务状态一致
3. **提供完整错误处理**：包括自动重试、状态跟踪、前端通知
4. **保持系统健壮性**：即使 Stronghold 初始化失败，应用仍能正常运行
5. **向后兼容**：现有 API 完全不变，所有现有功能正常工作

## 注意事项

1. **初始化顺序很重要**：必须先初始化 Stronghold 服务，再初始化依赖它的其他服务
2. **超时设置合理**：等待时间应该足够长以完成初始化，但不能太长影响用户体验
3. **错误处理完善**：应该为所有可能的失败情况提供合适的处理方案
4. **前端状态同步**：前端应该监听相关事件并相应地更新 UI 状态

这个解决方案不仅解决了当前的 AppHandle 依赖问题，还为未来的扩展和维护奠定了坚实的基础。 