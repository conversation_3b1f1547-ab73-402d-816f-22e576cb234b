# Stronghold 密码提供器使用指南

本文档介绍如何使用新的密码提供器模块来管理 Stronghold 的密钥，以及如何与后端管理功能集成。

## 概述

密码提供器模块提供了安全的密钥管理功能，包括：

- 🔐 **安全密码存储**：使用 `zeroize` 确保内存安全清理
- 🔄 **多种密码来源**：环境变量、用户输入、系统密钥链等
- 📊 **密码强度评估**：自动评估并强制密码强度要求
- ⏰ **密码过期管理**：支持密码时效性控制
- 🔧 **环境适配**：开发环境和生产环境不同的安全策略

## 快速开始

### 1. 应用启动时初始化

```rust
use crate::crypto::stronghold::{initialize_password_provider, PasswordProvider};

// 在应用启动时初始化密码提供器
async fn initialize_app() -> Result<(), Box<dyn std::error::Error>> {
    // 检测环境（生产环境 vs 开发环境）
    let is_production = std::env::var("TAURI_ENV")
        .map(|env| env == "production")
        .unwrap_or(false);
    
    // 初始化全局密码提供器
    initialize_password_provider(is_production).await?;
    
    log::info!("密码提供器初始化完成，生产模式: {}", is_production);
    Ok(())
}
```

### 2. 设置密码

```rust
use crate::crypto::stronghold::{get_global_password_provider, PasswordSource};

async fn setup_password() -> Result<(), Box<dyn std::error::Error>> {
    let provider = get_global_password_provider();
    let provider = provider.read().await;
    
    // 方法1：从环境变量加载
    provider.load_from_environment("STRONGHOLD_PASSWORD").await?;
    
    // 方法2：手动设置密码
    let password = "MySecurePassword123!".to_string();
    provider.set_password(password, PasswordSource::UserInput).await?;
    
    // 方法3：使用默认密码（仅开发环境）
    provider.use_default_password().await?;
    
    Ok(())
}
```

### 3. 与后端管理集成

```rust
use crate::crypto::stronghold::{
    UnifiedStorageAdapter, StrongholdManager,
    prepare_password_for_backend_enable, validate_backend_password,
    cleanup_password_for_backend_disable
};

async fn enable_stronghold_backend(
    adapter: &mut UnifiedStorageAdapter,
    manager: StrongholdManager
) -> Result<(), Box<dyn std::error::Error>> {
    // 1. 准备密码
    prepare_password_for_backend_enable().await?;
    
    // 2. 验证密码
    let password_valid = validate_backend_password("Stronghold").await?;
    if !password_valid {
        return Err("密码验证失败".into());
    }
    
    // 3. 启用后端
    adapter.enable_stronghold_backend(manager).await?;
    
    log::info!("Stronghold 后端已成功启用");
    Ok(())
}

async fn disable_stronghold_backend(
    adapter: &mut UnifiedStorageAdapter
) -> Result<(), Box<dyn std::error::Error>> {
    // 1. 禁用后端
    adapter.disable_stronghold_backend().await?;
    
    // 2. 清理密码（可选）
    cleanup_password_for_backend_disable().await?;
    
    log::info!("Stronghold 后端已禁用");
    Ok(())
}
```

## 配置选项

### 开发环境配置

```rust
use crate::crypto::stronghold::{PasswordProvider, PasswordStrength};

let provider = PasswordProvider::for_development();
// 特点：
// - 密码最大使用时间：24小时
// - 不强制密码强度检查
// - 最小密码强度：弱
// - 允许使用默认密码
```

### 生产环境配置

```rust
let provider = PasswordProvider::for_production();
// 特点：
// - 密码最大使用时间：30分钟
// - 强制密码强度检查
// - 最小密码强度：非常强
// - 不允许使用默认密码
```

### 自定义配置

```rust
let mut provider = PasswordProvider::new();
provider.update_config(
    Some(Duration::from_secs(1800)),    // 30分钟过期
    Some(true),                         // 强制密码强度
    Some(PasswordStrength::Strong),     // 最小强度要求
);
```

## 密码来源

### 1. 环境变量

```bash
# 设置环境变量
export STRONGHOLD_PASSWORD="YourSecurePassword123!"
```

```rust
// 从环境变量加载
provider.load_from_environment("STRONGHOLD_PASSWORD").await?;
```

### 2. 用户输入

```rust
// 从用户输入设置
let user_password = get_password_from_user(); // 你的用户输入函数
provider.set_password(user_password, PasswordSource::UserInput).await?;
```

### 3. 配置文件

```rust
// 从配置文件加载
let config_password = load_password_from_config(); // 你的配置加载函数
provider.set_password(config_password, PasswordSource::ConfigFile("config.toml".to_string())).await?;
```

### 4. 系统密钥链

```rust
// 从系统密钥链加载（需要实现具体的密钥链访问逻辑）
let keychain_password = load_password_from_keychain(); // 你的密钥链访问函数
provider.set_password(keychain_password, PasswordSource::SystemKeychain).await?;
```

## 密码强度评估

密码强度自动评估基于以下因素：

```rust
// 密码强度评估规则
fn evaluate_password_strength(password: &[u8]) -> PasswordStrength {
    // 长度评分：8+ 字符（+1），12+ 字符（+2）
    // 字符类型：小写字母、大写字母、数字、特殊字符（各+1）
    // 复杂性：16+ 字符（+1），3+ 特殊字符（+1）
    
    // 评分结果：
    // 0-2分：弱密码
    // 3-4分：中等密码
    // 5-6分：强密码
    // 7+分：非常强的密码
}
```

### 密码强度示例

```rust
// 弱密码
assert_eq!(evaluate_password_strength(b"123"), PasswordStrength::Weak);
assert_eq!(evaluate_password_strength(b"password"), PasswordStrength::Weak);

// 中等密码
assert_eq!(evaluate_password_strength(b"Password123"), PasswordStrength::Medium);

// 强密码
assert_eq!(evaluate_password_strength(b"Password123!"), PasswordStrength::Strong);

// 非常强的密码
assert_eq!(evaluate_password_strength(b"VeryStrongPassword123!@#$"), PasswordStrength::VeryStrong);
```

## 生命周期管理

### 密码过期处理

```rust
async fn check_password_expiry() -> Result<(), Box<dyn std::error::Error>> {
    let provider = get_global_password_provider();
    let provider = provider.read().await;
    
    // 验证密码是否过期
    match provider.validate_password().await {
        Ok(()) => {
            log::info!("密码有效");
        }
        Err(e) if e.to_string().contains("过期") => {
            log::warn!("密码已过期，需要重新设置");
            // 重新加载密码
            provider.load_from_environment("STRONGHOLD_PASSWORD").await?;
        }
        Err(e) => {
            log::error!("密码验证失败: {}", e);
            return Err(e.into());
        }
    }
    
    Ok(())
}
```

### 定期密码检查

```rust
use tokio::time::{interval, Duration};

async fn periodic_password_check() {
    let mut interval = interval(Duration::from_secs(300)); // 每5分钟检查一次
    
    loop {
        interval.tick().await;
        
        if let Err(e) = check_password_expiry().await {
            log::error!("定期密码检查失败: {}", e);
        }
    }
}
```

### 应用关闭时清理

```rust
async fn cleanup_on_shutdown() {
    let provider = get_global_password_provider();
    let provider = provider.read().await;
    
    // 清除密码
    provider.clear_password().await;
    log::info!("应用关闭，密码已清理");
}
```

## 错误处理

### 常见错误处理

```rust
use crate::crypto::stronghold::StrongholdError;

async fn handle_password_errors() {
    let provider = get_global_password_provider();
    let provider = provider.read().await;
    
    match provider.validate_password().await {
        Ok(()) => {
            log::info!("密码验证通过");
        }
        Err(StrongholdError::ConfigValidationFailed(msg)) if msg.contains("过期") => {
            log::warn!("密码已过期，尝试重新加载");
            // 处理密码过期
            if let Err(e) = provider.load_from_environment("STRONGHOLD_PASSWORD").await {
                log::error!("重新加载密码失败: {}", e);
            }
        }
        Err(StrongholdError::ConfigValidationFailed(msg)) if msg.contains("强度") => {
            log::error!("密码强度不足: {}", msg);
            // 提示用户设置更强的密码
        }
        Err(e) => {
            log::error!("密码验证失败: {}", e);
        }
    }
}
```

### 故障转移处理

```rust
async fn handle_password_failure_with_fallback() -> Result<(), Box<dyn std::error::Error>> {
    let provider = get_global_password_provider();
    let provider = provider.read().await;
    
    // 尝试主要密码源
    if let Err(_) = provider.load_from_environment("STRONGHOLD_PASSWORD").await {
        log::warn!("主要密码源失败，尝试备用密码源");
        
        // 尝试备用密码源
        if let Err(_) = provider.load_from_environment("STRONGHOLD_PASSWORD_BACKUP").await {
            log::warn!("备用密码源失败，尝试默认密码");
            
            // 最后尝试默认密码（仅开发环境）
            if !provider.enforce_strength {
                provider.use_default_password().await?;
            } else {
                return Err("所有密码源都失败，且不允许使用默认密码".into());
            }
        }
    }
    
    Ok(())
}
```

## 安全最佳实践

### 1. 密码安全

```rust
// ✅ 好的做法
// - 使用强密码
// - 定期更换密码
// - 从安全的来源获取密码
// - 设置合适的过期时间

// ❌ 避免的做法
// - 硬编码密码
// - 使用弱密码
// - 在日志中记录密码
// - 密码永不过期
```

### 2. 环境隔离

```rust
// 开发环境
if cfg!(debug_assertions) {
    initialize_password_provider(false).await?; // 宽松策略
} else {
    initialize_password_provider(true).await?;  // 严格策略
}
```

### 3. 日志安全

```rust
// ✅ 安全的日志记录
log::info!("密码已设置，来源: {:?}", source);
log::debug!("密码强度: {:?}", strength);

// ❌ 不安全的日志记录
// log::info!("密码内容: {}", password); // 永远不要这样做！
```

## 监控和调试

### 获取密码状态

```rust
async fn get_password_status() {
    let provider = get_global_password_provider();
    let provider = provider.read().await;
    
    if let Some((source, strength, age)) = provider.get_password_info().await {
        log::info!("密码状态:");
        log::info!("  来源: {:?}", source);
        log::info!("  强度: {:?}", strength);
        log::info!("  年龄: {:?}", age);
    } else {
        log::warn!("未设置密码");
    }
}
```

### 后端一致性检查

```rust
async fn check_system_consistency(adapter: &UnifiedStorageAdapter) {
    let issues = adapter.check_backend_consistency().await;
    
    if issues.is_empty() {
        log::info!("系统一致性检查通过");
    } else {
        log::warn!("发现 {} 个一致性问题:", issues.len());
        for issue in issues {
            log::warn!("  - {}", issue);
        }
    }
}
```

## 总结

新的密码提供器模块提供了：

1. **安全性**：内存安全清理、密码强度验证、过期管理
2. **灵活性**：多种密码来源、环境适配、自定义配置
3. **可靠性**：错误处理、故障转移、一致性检查
4. **易用性**：简单的 API、详细的日志、完整的文档

通过与后端管理功能的深度集成，确保了 Stronghold 初始化和后端管理的完美匹配，提供了企业级的安全存储解决方案。 