// Stronghold 安全存储模块
// 基于 tauri-plugin-stronghold 实现跨平台安全存储方案

pub mod config;
pub mod manager;
pub mod adapter;
pub mod factory;
pub mod error;
pub mod commands;
pub mod password_provider;
pub mod initialization;

// 测试模块
#[cfg(test)]
pub mod tests;

// 重新导出主要类型和函数
pub use config::{StrongholdConfig, AdapterConfig, BackendType, FailoverStrategy};
pub use manager::StrongholdManager;
pub use adapter::{StrongholdAdapter, UnifiedStorageAdapter};
pub use factory::StrongholdFactory;
pub use error::{StrongholdError, StrongholdResult};
pub use password_provider::{
    PasswordProvider, PasswordStrength, PasswordSource,
    stronghold_password_provider, initialize_password_provider,
    get_global_password_provider, validate_backend_password,
    prepare_password_for_backend_enable, cleanup_password_for_backend_disable,
};
pub use initialization::{
    InitializationStatus, StrongholdInitializationService,
    get_global_stronghold_init_service, initialize_global_stronghold_service,
    get_global_unified_adapter, get_global_stronghold_manager,
    get_global_stronghold_status, retry_global_stronghold_initialization,
    reset_global_stronghold_service,
    // 增强的可用性检测功能
    StrongholdAvailabilityStatus, PluginStatus, ReadWriteTestResult,
    check_enhanced_stronghold_availability, wait_for_stronghold_fully_available,
};

// 统一存储适配器已在上面导出

// 导出常用的配置预设
pub use config::{
    create_default_config,
    create_high_security_config,
    create_fast_config,
}; 