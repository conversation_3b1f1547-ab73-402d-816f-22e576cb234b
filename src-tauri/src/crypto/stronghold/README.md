# Stronghold 安全存储模块

基于 `tauri-plugin-stronghold` 实现的跨平台安全存储方案，为 Tauri 2.5.0 密码管理应用提供企业级安全存储功能。

## 功能特性

### 🔒 安全性
- **硬件级加密**：基于 IOTA Stronghold 的军用级安全存储
- **内存安全**：使用 `zeroize` 确保敏感数据安全清除
- **密码策略**：支持复杂度验证和安全策略
- **防时序攻击**：使用常时间比较算法

### 🔄 可靠性
- **故障转移**：支持 Stronghold 和 Keychain 之间的自动切换
- **健康检查**：定期检测后端可用性
- **重试机制**：自动重试失败的操作
- **快照备份**：自动数据持久化

### ⚡ 性能
- **异步非阻塞**：全异步 API 设计
- **并发安全**：支持多线程并发访问
- **缓存优化**：智能实例缓存和复用
- **批量操作**：支持批量数据处理

### 🌐 跨平台
- **桌面支持**：Windows、macOS、Linux
- **移动支持**：iOS、Android
- **统一接口**：跨平台一致的 API

## 架构设计

```
┌─────────────────────────────────────────────────────────────┐
│                    StrongholdFactory                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   实例缓存      │ │   配置管理      │ │   能力检查      ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                UnifiedStorageAdapter                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐│
│  │   故障转移      │ │   健康检查      │ │   负载均衡      ││
│  └─────────────────┘ └─────────────────┘ └─────────────────┘│
└─────────────────────────────────────────────────────────────┘
                              │
                    ┌─────────┴─────────┐
                    ▼                   ▼
┌─────────────────────────┐   ┌─────────────────────────┐
│   StrongholdAdapter     │   │   KeychainManager       │
│  ┌─────────────────────┐│   │  ┌─────────────────────┐│
│  │ StrongholdManager   ││   │  │   系统密钥链        ││
│  └─────────────────────┘│   │  └─────────────────────┘│
└─────────────────────────┘   └─────────────────────────┘
```

## 快速开始

### 1. 基本使用

```rust
use crate::crypto::stronghold::{StrongholdFactory, BackendType, FailoverStrategy};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // 创建工厂实例
    let factory = StrongholdFactory::default();
    
    // 创建统一存储适配器
    let adapter = factory.create_unified_adapter(None).await?;
    
    // 存储敏感数据
    let key = "user_password";
    let password = b"super_secret_password";
    adapter.store(key, password).await?;
    
    // 获取数据
    let retrieved = adapter.get(key).await?;
    assert_eq!(retrieved, Some(password.to_vec()));
    
    // 删除数据
    adapter.remove(key).await?;
    
    Ok(())
}
```

### 2. 高安全性配置

```rust
// 创建高安全性适配器（仅使用 Stronghold）
let adapter = factory.create_high_security_adapter().await?;

// 存储加密密钥
let encryption_key = generate_secure_key(); // 32 字节密钥
adapter.store("master_key", &encryption_key).await?;
```

### 3. 故障转移配置

```rust
// 创建带自动故障转移的适配器
let adapter = factory.create_failover_adapter(
    BackendType::Stronghold,  // 主后端
    FailoverStrategy::Automatic  // 自动故障转移
).await?;

// 即使 Stronghold 不可用，也会自动切换到 Keychain
adapter.store("api_token", b"token_value").await?;
```

## 配置选项

### StrongholdConfig

```rust
use crate::crypto::stronghold::config::*;

// 默认配置
let config = create_default_config();

// 高安全性配置
let config = create_high_security_config();

// 快速配置（适合开发环境）
let config = create_fast_config();

// 自定义配置
let config = StrongholdConfig {
    vault_path: "custom_vault.stronghold".into(),
    client_name: "MyApp".to_string(),
    password_policy: PasswordPolicy {
        min_length: 12,
        require_uppercase: true,
        require_lowercase: true,
        require_numbers: true,
        require_symbols: true,
    },
    snapshot_interval: Duration::from_secs(300),
    max_concurrent_operations: 10,
    operation_timeout: Duration::from_secs(30),
};
```

### AdapterConfig

```rust
let config = AdapterConfig {
    primary_backend: BackendType::Stronghold,
    enable_stronghold: true,
    enable_keychain: true,
    failover_strategy: FailoverStrategy::Smart,
    stronghold_config: Some(create_default_config()),
    health_check_interval: Duration::from_secs(300),
    max_retry_attempts: 3,
    retry_delay: Duration::from_secs(1),
};
```

## 故障转移策略

### 1. 无故障转移 (None)
```rust
// 仅使用主后端，不进行故障转移
let adapter = factory.create_failover_adapter(
    BackendType::Stronghold,
    FailoverStrategy::None
).await?;
```

### 2. 自动故障转移 (Automatic)
```rust
// 检测到故障时自动切换到备用后端
let adapter = factory.create_failover_adapter(
    BackendType::Stronghold,
    FailoverStrategy::Automatic
).await?;
```

### 3. 手动故障转移 (Manual)
```rust
// 需要手动触发故障转移
let adapter = factory.create_failover_adapter(
    BackendType::Stronghold,
    FailoverStrategy::Manual
).await?;

// 手动切换后端
adapter.switch_backend(BackendType::Keychain).await?;
```

### 4. 智能故障转移 (Smart)
```rust
// 基于健康检查和性能指标智能选择后端
let adapter = factory.create_failover_adapter(
    BackendType::Stronghold,
    FailoverStrategy::Smart
).await?;
```

## 高级功能

### 1. 批量操作

```rust
// 批量存储
let data = vec![
    ("key1", b"value1".to_vec()),
    ("key2", b"value2".to_vec()),
    ("key3", b"value3".to_vec()),
];

for (key, value) in data {
    adapter.store(&key, &value).await?;
}

// 批量获取
let keys = vec!["key1", "key2", "key3"];
let mut results = Vec::new();

for key in keys {
    if let Some(value) = adapter.get(key).await? {
        results.push((key, value));
    }
}
```

### 2. 健康监控

```rust
// 获取后端健康状态
let health = adapter.get_backend_health().await;
for result in health {
    println!("Backend {:?}: {:?} ({}ms)", 
             result.backend, 
             result.status, 
             result.response_time.as_millis());
}

// 获取故障转移统计
let failover_count = adapter.get_failover_count().await;
println!("Failover count: {}", failover_count);
```

### 3. 缓存管理

```rust
// 获取缓存统计
let stats = factory.get_cache_stats().await;
println!("Cache entries: {}, Total accesses: {}", 
         stats.total_entries, stats.total_access_count);

// 清理过期缓存
factory.cleanup_cache(3600).await; // 清理1小时前的缓存

// 清空所有缓存
factory.clear_cache().await;
```

### 4. 能力检查

```rust
// 检查系统能力
let capabilities = factory.check_capabilities().await;
println!("Stronghold available: {}", capabilities.stronghold_available);
println!("Keychain available: {}", capabilities.keychain_available);
println!("Failover supported: {}", capabilities.failover_supported);
```

## 错误处理

```rust
use crate::crypto::stronghold::{StrongholdError, StrongholdResult};

match adapter.store("key", b"value").await {
    Ok(()) => println!("Store successful"),
    Err(StrongholdError::BackendUnavailable(backend)) => {
        println!("Backend {} unavailable", backend);
    }
    Err(StrongholdError::TimeoutError(msg)) => {
        println!("Operation timed out: {}", msg);
    }
    Err(StrongholdError::InvalidData(msg)) => {
        println!("Invalid data: {}", msg);
    }
    Err(e) => {
        println!("Other error: {}", e);
    }
}
```

## 测试

### 运行测试

```bash
# 运行所有测试
cargo test --package secure-password --lib crypto::stronghold

# 运行特定测试模块
cargo test --package secure-password --lib crypto::stronghold::tests::basic_tests

# 运行性能测试
cargo test --package secure-password --lib crypto::stronghold::tests::performance_tests --release
```

### 测试覆盖率

```bash
# 安装 cargo-tarpaulin
cargo install cargo-tarpaulin

# 生成测试覆盖率报告
cargo tarpaulin --out Html --output-dir coverage
```

## 最佳实践

### 1. 安全性
- 使用高安全性配置处理敏感数据
- 定期更换加密密钥
- 启用密码策略验证
- 使用 `zeroize` 清理敏感内存

### 2. 可靠性
- 启用故障转移机制
- 配置适当的重试策略
- 定期执行健康检查
- 监控故障转移事件

### 3. 性能
- 使用缓存减少重复创建
- 批量处理大量数据
- 配置合适的超时时间
- 监控操作性能

### 4. 维护性
- 使用工厂模式创建实例
- 统一错误处理策略
- 记录详细的操作日志
- 定期清理过期数据

## 故障排除

### 常见问题

1. **Stronghold 初始化失败**
   ```rust
   // 检查文件权限和路径
   let config = StrongholdConfig {
       vault_path: std::env::temp_dir().join("test.stronghold"),
       ..Default::default()
   };
   ```

2. **Keychain 访问被拒绝**
   ```rust
   // 在 macOS 上可能需要用户授权
   // 使用仅 Stronghold 配置作为备选
   let adapter = factory.create_high_security_adapter().await?;
   ```

3. **操作超时**
   ```rust
   // 增加超时时间
   let mut config = create_default_config();
   config.operation_timeout = Duration::from_secs(60);
   ```

### 调试技巧

```rust
// 启用详细日志
env_logger::init();

// 检查后端状态
let health = adapter.get_backend_health().await;
for result in health {
    log::info!("Backend health: {:?}", result);
}

// 监控故障转移
let failover_count = adapter.get_failover_count().await;
if failover_count > 0 {
    log::warn!("Failover occurred {} times", failover_count);
}
```

## 版本兼容性

- **Tauri**: 2.5.0+
- **tauri-plugin-stronghold**: 2.2.0+
- **Rust**: 1.70+
- **支持平台**: Windows, macOS, Linux, iOS, Android

## 许可证

本模块遵循项目的整体许可证。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个模块。

---

更多详细信息请参考源代码中的文档注释。 