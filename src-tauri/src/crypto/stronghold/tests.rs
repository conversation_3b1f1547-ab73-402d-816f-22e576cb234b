// Stronghold 模块测试
// 注意：由于 StrongholdManager 需要 AppHandle，大部分实际功能测试需要在集成测试中进行

use super::{
    StrongholdAdapter, UnifiedStorageAdapter,
    StrongholdFactory, BackendType, FailoverStrategy,
    AdapterConfig, StrongholdError,
    factory::FactoryOptions,
    config::{create_default_config, create_fast_config, create_high_security_config}
};

/// 测试工具函数
mod test_utils {
    use super::*;
    
    /// 创建测试用的工厂实例
    pub fn create_test_factory() -> StrongholdFactory {
        let options = FactoryOptions {
            enable_cache: true,
            cache_size_limit: 10,
            enable_warmup: false, // 测试时禁用预热以加快速度
            warmup_timeout_secs: 5,
            enable_health_check: true,
            health_check_interval_secs: 60,
            enable_high_security: false,
            enable_fast: false,
            enable_keychain_only: false,
            enable_failover: false,
            primary_backend: BackendType::Stronghold,
            failover_strategy: FailoverStrategy::Automatic,
            high_security_config: None,
            fast_config: None,
            adapter_config: None,
            enable_high_security_mode: false,
            enable_caching: true,
            cache_size: 100,
            enable_prewarming: false,
            timeout: std::time::Duration::from_secs(30),
        };
        StrongholdFactory::new(options)
    }
    
    /// 生成测试数据
    pub fn generate_test_data(size: usize) -> Vec<u8> {
        (0..size).map(|i| (i % 256) as u8).collect()
    }
    
    /// 生成随机测试键
    pub fn generate_test_key(prefix: &str) -> String {
        use std::time::{SystemTime, UNIX_EPOCH};
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_nanos();
        format!("{}_{}", prefix, timestamp)
    }
}

/// 配置测试
#[cfg(test)]
mod config_tests {
    use super::*;

    #[test]
    fn test_stronghold_config_creation() {
        let default_config = create_default_config();
        assert!(default_config.validate().is_ok());
        
        let fast_config = create_fast_config();
        assert!(fast_config.validate().is_ok());
        
        let high_security_config = create_high_security_config();
        assert!(high_security_config.validate().is_ok());
    }

    #[test]
    fn test_adapter_config_creation() {
        let config = AdapterConfig::default();
        assert!(config.validate().is_ok());
        assert_eq!(config.primary_backend, BackendType::Stronghold);
        assert!(config.enable_stronghold);
    }

    #[test]
    fn test_backend_type_availability() {
        assert!(BackendType::Stronghold.is_available());
        
        // Keychain 可用性取决于平台
        #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
        assert!(BackendType::Keychain.is_available());
        
        #[cfg(not(any(target_os = "windows", target_os = "macos", target_os = "linux")))]
        assert!(!BackendType::Keychain.is_available());
    }
}

/// 工厂测试
#[cfg(test)]
mod factory_tests {
    use super::*;
    use test_utils::*;

    #[test]
    fn test_factory_creation() {
        let factory = create_test_factory();
        assert!(factory.get_options().enable_cache);
        assert_eq!(factory.get_options().cache_size_limit, 10);
    }

    #[test]
    fn test_factory_presets() {
        let default_factory = StrongholdFactory::default();
        assert!(default_factory.get_options().enable_cache);
        
        let with_default = StrongholdFactory::with_default_config();
        assert!(with_default.get_options().enable_cache);
        
        let with_high_security = StrongholdFactory::with_high_security_config();
        assert!(!with_high_security.get_options().enable_caching); // 高安全性模式禁用缓存
        
        let with_fast = StrongholdFactory::with_fast_config();
        assert!(with_fast.get_options().enable_prewarming);
    }

    #[tokio::test]
    async fn test_cache_operations() {
        let factory = create_test_factory();
        
        // 测试缓存统计
        let stats = factory.get_cache_stats().await;
        assert_eq!(stats.total_entries, 0);
        assert_eq!(stats.total_access_count, 0);
        
        // 测试缓存清理
        factory.cleanup_cache(3600).await; // 1小时
        factory.clear_cache().await;
        
        let stats_after_clear = factory.get_cache_stats().await;
        assert_eq!(stats_after_clear.total_entries, 0);
    }

    #[tokio::test]
    async fn test_capabilities_check() {
        let factory = create_test_factory();
        let capabilities = factory.check_capabilities().await;
        
        // 在测试环境中，Stronghold 不可用（因为没有 AppHandle）
        assert!(!capabilities.stronghold_available);
        
        // Keychain 在某些平台上可能可用（如 macOS）
        // 故障转移需要两个后端都可用
        if capabilities.stronghold_available && capabilities.keychain_available {
            assert!(capabilities.failover_supported);
        } else {
            assert!(!capabilities.failover_supported);
        }
        
        // 检查支持的后端列表与可用性一致
        if capabilities.stronghold_available {
            assert!(capabilities.supported_backends.contains(&BackendType::Stronghold));
        }
        if capabilities.keychain_available {
            assert!(capabilities.supported_backends.contains(&BackendType::Keychain));
        }
        
        // 支持的后端数量应该等于可用后端的数量
        let expected_count = 
            (if capabilities.stronghold_available { 1 } else { 0 }) +
            (if capabilities.keychain_available { 1 } else { 0 });
        assert_eq!(capabilities.supported_backends.len(), expected_count);
    }
}

/// 错误处理测试
#[cfg(test)]
mod error_tests {
    use super::*;

    #[test]
    fn test_error_types() {
        let errors = vec![
            StrongholdError::NotInitialized,
            StrongholdError::BackendUnavailable { 
                backend: "test".to_string() 
            },
            StrongholdError::ConfigValidationFailed("test".to_string()),
            StrongholdError::OperationFailed("test".to_string()),
        ];

        for error in errors {
            let error_string = error.to_string();
            assert!(!error_string.is_empty());
        }
    }

    #[test]
    fn test_error_conversion() {
        let stronghold_error = StrongholdError::KeyNotFound {
            key: "test_key".to_string(),
        };
        
        let vault_error: crate::errors::VaultError = stronghold_error.into();
        assert!(matches!(vault_error, crate::errors::VaultError::InternalError(_)));
    }
}

/// 适配器创建测试（不需要实际初始化）
#[cfg(test)]
mod adapter_tests {
    use super::*;
    use test_utils::*;

    #[tokio::test]
    async fn test_stronghold_adapter_creation_failure() {
        // 由于没有 AppHandle，这应该失败
        let config = create_fast_config();
        let result = StrongholdAdapter::new(config).await;
        assert!(result.is_err());
        
        // 验证错误类型
        match result.unwrap_err() {
            StrongholdError::InitializationFailed(_) => {
                // 这是预期的错误
            }
            other => {
                panic!("Unexpected error type: {:?}", other);
            }
        }
    }

    #[tokio::test]
    async fn test_unified_adapter_creation() {
        let config = AdapterConfig::default();
        let result = UnifiedStorageAdapter::new(config).await;
        
        // 由于没有实际的后端可用，这可能会失败，但我们可以测试错误处理
        match result {
            Ok(_) => {
                // 如果成功创建，说明适配器逻辑正常
            }
            Err(e) => {
                // 失败也是正常的，因为我们没有真实的环境
                println!("Unified adapter creation failed as expected: {:?}", e);
            }
        }
    }
}

// 注意：由于测试函数是私有的，run_all_tests 函数已被移除
// 请使用 cargo test 命令运行各个测试模块 