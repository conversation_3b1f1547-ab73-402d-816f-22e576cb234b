// Stronghold 命令模块
// 提供前端调用的 Tauri 命令接口

use std::sync::Arc;
use serde::{Deserialize, Serialize};
use tauri::AppHandle;
use tokio::sync::RwLock;
use base64::Engine;

use super::{
    StrongholdFactory, UnifiedStorageAdapter,
    StrongholdConfig, AdapterConfig, BackendType, FailoverStrategy,
    config::{create_default_config, create_high_security_config, create_fast_config}
};

/// Stronghold 服务状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrongholdServiceStatus {
    /// 是否已初始化
    pub initialized: bool,
    /// 当前状态描述
    pub status: String,
    /// 可用的后端类型
    pub available_backends: Vec<String>,
    /// 当前活跃后端
    pub active_backend: Option<String>,
    /// 故障转移次数
    pub failover_count: u32,
    /// 操作统计
    pub operation_stats: std::collections::HashMap<String, u64>,
}

/// Stronghold 初始化参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrongholdInitParams {
    /// 主密码
    pub password: String,
    /// 配置类型 ("default", "high_security", "fast")
    pub config_type: Option<String>,
    /// 自定义配置
    pub custom_config: Option<StrongholdConfig>,
    /// 是否启用故障转移
    pub enable_failover: Option<bool>,
    /// 主后端类型
    pub primary_backend: Option<String>,
}

/// 存储操作参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StoreParams {
    /// 存储键
    pub key: String,
    /// 存储值（Base64编码）
    pub value: String,
    /// 数据类型
    pub item_type: Option<String>,
}

/// 获取操作参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GetParams {
    /// 存储键
    pub key: String,
}

/// 存储项响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StorageItem {
    /// 数据内容（Base64编码）
    pub data: String,
    /// 数据类型
    pub item_type: String,
    /// 创建时间戳
    pub created_at: i64,
    /// 最后访问时间戳
    pub last_accessed: i64,
    /// 元数据
    pub metadata: std::collections::HashMap<String, String>,
}

/// 全局 Stronghold 工厂实例
static STRONGHOLD_FACTORY: once_cell::sync::Lazy<Arc<RwLock<Option<StrongholdFactory>>>> = 
    once_cell::sync::Lazy::new(|| Arc::new(RwLock::new(None)));

/// 全局统一存储适配器实例
static UNIFIED_ADAPTER: once_cell::sync::Lazy<Arc<RwLock<Option<Arc<UnifiedStorageAdapter>>>>> = 
    once_cell::sync::Lazy::new(|| Arc::new(RwLock::new(None)));

/// 初始化 Stronghold 服务
/// 
/// # 参数
/// * `app_handle` - Tauri 应用句柄
/// * `params` - 初始化参数
/// 
/// # 返回
/// * `Result<StrongholdServiceStatus, String>` - 服务状态或错误信息
#[tauri::command]
pub async fn initialize_stronghold_service(
    _app_handle: AppHandle,
    params: StrongholdInitParams,
) -> Result<StrongholdServiceStatus, String> {
    log::info!("开始初始化 Stronghold 服务");

    // 创建配置
    let stronghold_config = match params.custom_config {
        Some(config) => config,
        None => {
            match params.config_type.as_deref().unwrap_or("default") {
                "high_security" => create_high_security_config(),
                "fast" => create_fast_config(),
                _ => create_default_config(),
            }
        }
    };

    // 验证配置
    stronghold_config.validate().map_err(|e| e.to_string())?;

    // 创建工厂实例
    let factory = StrongholdFactory::default();

    // 创建适配器配置
    let primary_backend = match params.primary_backend.as_deref() {
        Some("keychain") => BackendType::Keychain,
        _ => BackendType::Stronghold,
    };

    let adapter_config = AdapterConfig {
        primary_backend,
        enable_stronghold: true,
        enable_keychain: params.enable_failover.unwrap_or(true),
        failover_strategy: if params.enable_failover.unwrap_or(true) {
            FailoverStrategy::Automatic
        } else {
            FailoverStrategy::None
        },
        stronghold_config: Some(stronghold_config),
        health_check_interval: std::time::Duration::from_secs(300),
        max_retry_attempts: 3,
        retry_delay: std::time::Duration::from_secs(1),
    };

    // 创建统一存储适配器
    let adapter = factory.create_unified_adapter(Some(adapter_config)).await
        .map_err(|e| format!("创建适配器失败: {}", e))?;

    // 存储全局实例
    {
        let mut factory_guard = STRONGHOLD_FACTORY.write().await;
        *factory_guard = Some(factory);
    }
    {
        let mut adapter_guard = UNIFIED_ADAPTER.write().await;
        *adapter_guard = Some(adapter.clone());
    }

    // 获取服务状态
    let status = get_service_status_internal(&adapter).await;

    log::info!("Stronghold 服务初始化成功");
    Ok(status)
}

/// 获取 Stronghold 服务状态
#[tauri::command]
pub async fn get_stronghold_service_status() -> Result<StrongholdServiceStatus, String> {
    let adapter_guard = UNIFIED_ADAPTER.read().await;
    
    match adapter_guard.as_ref() {
        Some(adapter) => {
            let status = get_service_status_internal(adapter).await;
            Ok(status)
        }
        None => {
            Ok(StrongholdServiceStatus {
                initialized: false,
                status: "未初始化".to_string(),
                available_backends: vec![],
                active_backend: None,
                failover_count: 0,
                operation_stats: std::collections::HashMap::new(),
            })
        }
    }
}

/// 存储数据到 Stronghold
#[tauri::command]
pub async fn stronghold_store(params: StoreParams) -> Result<(), String> {
    let adapter_guard = UNIFIED_ADAPTER.read().await;
    
    let adapter = adapter_guard.as_ref()
        .ok_or_else(|| "Stronghold 服务未初始化".to_string())?;

    // 解码 Base64 数据
    let data = base64::engine::general_purpose::STANDARD
        .decode(&params.value)
        .map_err(|e| format!("Base64 解码失败: {}", e))?;

    adapter.store(&params.key, &data).await
        .map_err(|e| format!("存储失败: {}", e))?;

    log::debug!("成功存储数据: key={}", params.key);
    Ok(())
}

/// 从 Stronghold 获取数据
#[tauri::command]
pub async fn stronghold_get(params: GetParams) -> Result<Option<String>, String> {
    let adapter_guard = UNIFIED_ADAPTER.read().await;
    
    let adapter = adapter_guard.as_ref()
        .ok_or_else(|| "Stronghold 服务未初始化".to_string())?;

    let data = adapter.get(&params.key).await
        .map_err(|e| format!("获取失败: {}", e))?;

    match data {
        Some(bytes) => {
            let encoded = base64::engine::general_purpose::STANDARD.encode(&bytes);
            log::debug!("成功获取数据: key={}", params.key);
            Ok(Some(encoded))
        }
        None => {
            log::debug!("数据不存在: key={}", params.key);
            Ok(None)
        }
    }
}

/// 从 Stronghold 删除数据
#[tauri::command]
pub async fn stronghold_remove(params: GetParams) -> Result<bool, String> {
    let adapter_guard = UNIFIED_ADAPTER.read().await;
    
    let adapter = adapter_guard.as_ref()
        .ok_or_else(|| "Stronghold 服务未初始化".to_string())?;

    let removed = adapter.remove(&params.key).await
        .map_err(|e| format!("删除失败: {}", e))?;

    log::debug!("删除操作完成: key={}, removed={}", params.key, removed);
    Ok(removed)
}

/// 列出所有存储的键
#[tauri::command]
pub async fn stronghold_list_keys() -> Result<Vec<String>, String> {
    let adapter_guard = UNIFIED_ADAPTER.read().await;
    
    let adapter = adapter_guard.as_ref()
        .ok_or_else(|| "Stronghold 服务未初始化".to_string())?;

    let keys = adapter.list_keys().await
        .map_err(|e| format!("列出键失败: {}", e))?;

    log::debug!("成功列出 {} 个键", keys.len());
    Ok(keys)
}

/// 检查键是否存在
#[tauri::command]
pub async fn stronghold_exists(params: GetParams) -> Result<bool, String> {
    let adapter_guard = UNIFIED_ADAPTER.read().await;
    
    let adapter = adapter_guard.as_ref()
        .ok_or_else(|| "Stronghold 服务未初始化".to_string())?;

    // 通过尝试获取数据来检查是否存在
    let exists = adapter.get(&params.key).await
        .map_err(|e| format!("检查存在性失败: {}", e))?
        .is_some();

    log::debug!("检查键存在性: key={}, exists={}", params.key, exists);
    Ok(exists)
}

/// 切换后端
#[tauri::command]
pub async fn stronghold_switch_backend(backend: String) -> Result<(), String> {
    let adapter_guard = UNIFIED_ADAPTER.read().await;
    
    let adapter = adapter_guard.as_ref()
        .ok_or_else(|| "Stronghold 服务未初始化".to_string())?;

    let backend_type = match backend.as_str() {
        "stronghold" => BackendType::Stronghold,
        "keychain" => BackendType::Keychain,
        _ => return Err(format!("不支持的后端类型: {}", backend)),
    };

    adapter.switch_backend(backend_type).await
        .map_err(|e| format!("切换后端失败: {}", e))?;

    log::info!("成功切换到后端: {}", backend);
    Ok(())
}

/// 获取后端健康状态
#[tauri::command]
pub async fn stronghold_get_backend_health() -> Result<Vec<serde_json::Value>, String> {
    let adapter_guard = UNIFIED_ADAPTER.read().await;
    
    let adapter = adapter_guard.as_ref()
        .ok_or_else(|| "Stronghold 服务未初始化".to_string())?;

    let health_results = adapter.get_backend_health().await;
    
    let health_json: Vec<serde_json::Value> = health_results.into_iter()
        .map(|result| serde_json::json!({
            "backend": format!("{:?}", result.backend),
            "status": format!("{:?}", result.status),
            "response_time_ms": result.response_time.as_millis(),
            "last_check": result.last_check.elapsed().as_secs(),
            "error_count": result.error_count,
        }))
        .collect();

    Ok(health_json)
}

/// 重置 Stronghold 服务
#[tauri::command]
pub async fn reset_stronghold_service() -> Result<(), String> {
    log::info!("重置 Stronghold 服务");

    // 清理全局实例
    {
        let mut factory_guard = STRONGHOLD_FACTORY.write().await;
        *factory_guard = None;
    }
    {
        let mut adapter_guard = UNIFIED_ADAPTER.write().await;
        *adapter_guard = None;
    }

    log::info!("Stronghold 服务重置完成");
    Ok(())
}

/// 检查 Stronghold 能力
#[tauri::command]
pub async fn check_stronghold_capabilities() -> Result<serde_json::Value, String> {
    let factory_guard = STRONGHOLD_FACTORY.read().await;
    
    let factory = factory_guard.as_ref()
        .ok_or_else(|| "Stronghold 工厂未初始化".to_string())?;

    let capabilities = factory.check_capabilities().await;

    Ok(serde_json::json!({
        "stronghold_available": capabilities.stronghold_available,
        "keychain_available": capabilities.keychain_available,
        "failover_supported": capabilities.failover_supported,
        "supported_backends": capabilities.supported_backends.iter()
            .map(|b| format!("{:?}", b))
            .collect::<Vec<_>>(),
    }))
}

/// 获取缓存统计信息
#[tauri::command]
pub async fn get_stronghold_cache_stats() -> Result<serde_json::Value, String> {
    let factory_guard = STRONGHOLD_FACTORY.read().await;
    
    let factory = factory_guard.as_ref()
        .ok_or_else(|| "Stronghold 工厂未初始化".to_string())?;

    let stats = factory.get_cache_stats().await;

    Ok(serde_json::json!({
        "total_entries": stats.total_entries,
        "total_access_count": stats.total_access_count,
        "avg_access_count": stats.avg_access_count,
        "cache_size_limit": stats.cache_size_limit,
    }))
}

/// 清理缓存
#[tauri::command]
pub async fn cleanup_stronghold_cache(max_age_secs: Option<u64>) -> Result<(), String> {
    let factory_guard = STRONGHOLD_FACTORY.read().await;
    
    let factory = factory_guard.as_ref()
        .ok_or_else(|| "Stronghold 工厂未初始化".to_string())?;

    let max_age = max_age_secs.unwrap_or(3600); // 默认1小时
    factory.cleanup_cache(max_age).await;

    log::info!("Stronghold 缓存清理完成，最大年龄: {}秒", max_age);
    Ok(())
}

// 内部辅助函数

/// 获取服务状态（内部使用）
async fn get_service_status_internal(adapter: &Arc<UnifiedStorageAdapter>) -> StrongholdServiceStatus {
    let active_backend = adapter.get_active_backend().await;
    let health_results = adapter.get_backend_health().await;
    let failover_count = adapter.get_failover_count().await;

    let available_backends: Vec<String> = health_results.iter()
        .filter(|h| matches!(h.status, super::adapter::BackendStatus::Available))
        .map(|h| format!("{:?}", h.backend))
        .collect();

    StrongholdServiceStatus {
        initialized: true,
        status: "已初始化".to_string(),
        available_backends,
        active_backend: Some(format!("{:?}", active_backend)),
        failover_count,
        operation_stats: std::collections::HashMap::new(), // 暂时为空，可以后续扩展
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_stronghold_init_params() {
        let params = StrongholdInitParams {
            password: "test_password".to_string(),
            config_type: Some("default".to_string()),
            custom_config: None,
            enable_failover: Some(true),
            primary_backend: Some("stronghold".to_string()),
        };

        assert_eq!(params.password, "test_password");
        assert_eq!(params.config_type, Some("default".to_string()));
        assert_eq!(params.enable_failover, Some(true));
    }

    #[test]
    fn test_store_params() {
        let params = StoreParams {
            key: "test_key".to_string(),
            value: "dGVzdF92YWx1ZQ==".to_string(), // "test_value" in base64
            item_type: Some("test_type".to_string()),
        };

        assert_eq!(params.key, "test_key");
        assert_eq!(params.value, "dGVzdF92YWx1ZQ==");
        assert_eq!(params.item_type, Some("test_type".to_string()));
    }

    #[test]
    fn test_service_status() {
        let status = StrongholdServiceStatus {
            initialized: true,
            status: "运行中".to_string(),
            available_backends: vec!["Stronghold".to_string(), "Keychain".to_string()],
            active_backend: Some("Stronghold".to_string()),
            failover_count: 0,
            operation_stats: std::collections::HashMap::new(),
        };

        assert!(status.initialized);
        assert_eq!(status.status, "运行中");
        assert_eq!(status.available_backends.len(), 2);
    }
} 