// Stronghold 初始化服务模块
// 负责在应用启动时正确初始化 Stronghold 服务

use std::sync::Arc;
use std::time::Duration;

use log;
use once_cell::sync::OnceCell;
use tauri::{AppHandle, Emitter, Manager};
use tokio::sync::RwLock;

use super::{
    StrongholdResult, StrongholdError, StrongholdManager, UnifiedStorageAdapter,
    AdapterConfig, BackendType, FailoverStrategy,
    config::{create_default_config, create_fast_config},
    password_provider::{initialize_password_provider, validate_backend_password},
};

/// 全局 Stronghold 初始化服务实例
static STRONGHOLD_INIT_SERVICE: OnceCell<Arc<RwLock<StrongholdInitializationService>>> = OnceCell::new();

/// Stronghold 初始化状态
#[derive(Debug, Clone, PartialEq)]
pub enum InitializationStatus {
    /// 未初始化
    NotInitialized,
    /// 正在初始化
    Initializing,
    /// 初始化成功
    Initialized,
    /// 初始化失败
    Failed(String),
    /// 密码提供器未就绪
    PasswordProviderNotReady,
    /// 等待 AppHandle
    WaitingForAppHandle,
}

/// Stronghold 初始化服务
/// 
/// 管理 Stronghold 的完整初始化流程，包括密码提供器、管理器和适配器的初始化
#[derive(Debug)]
pub struct StrongholdInitializationService {
    /// 初始化状态
    status: InitializationStatus,
    /// 应用句柄
    app_handle: Option<AppHandle>,
    /// 统一存储适配器
    adapter: Option<Arc<RwLock<UnifiedStorageAdapter>>>,
    /// Stronghold 管理器
    stronghold_manager: Option<Arc<RwLock<StrongholdManager>>>,
    /// 是否为生产环境
    is_production: bool,
    /// 初始化错误信息
    last_error: Option<String>,
    /// 初始化尝试次数
    initialization_attempts: u32,
    /// 最大重试次数
    max_retry_attempts: u32,
}

impl StrongholdInitializationService {
    /// 创建新的初始化服务实例
    pub fn new() -> Self {
        Self {
            status: InitializationStatus::NotInitialized,
            app_handle: None,
            adapter: None,
            stronghold_manager: None,
            is_production: std::env::var("TAURI_ENV")
                .map(|env| env == "production")
                .unwrap_or(false),
            last_error: None,
            initialization_attempts: 0,
            max_retry_attempts: 3,
        }
    }

    /// 设置 AppHandle
    /// 
    /// # 参数
    /// * `app_handle` - Tauri 应用句柄
    pub async fn set_app_handle(&mut self, app_handle: AppHandle) -> StrongholdResult<()> {
        log::info!("设置 Stronghold 初始化服务的 AppHandle");
        
        self.app_handle = Some(app_handle.clone());
        
        // 如果之前状态是等待 AppHandle，现在可以开始初始化
        if self.status == InitializationStatus::WaitingForAppHandle {
            log::info!("AppHandle 已设置，开始自动初始化 Stronghold");
            self.initialize().await?;
        }
        
        Ok(())
    }

    /// 完整的 Stronghold 初始化流程
    /// 
    /// # 返回
    /// * `StrongholdResult<()>` - 初始化结果
    pub async fn initialize(&mut self) -> StrongholdResult<()> {
        if self.status == InitializationStatus::Initializing {
            log::warn!("Stronghold 正在初始化中，跳过重复初始化");
            return Ok(());
        }

        self.status = InitializationStatus::Initializing;
        self.initialization_attempts += 1;
        
        log::info!("开始 Stronghold 完整初始化流程 (尝试 {}/{})", 
                   self.initialization_attempts, self.max_retry_attempts);

        // 发送初始化状态事件
        self.emit_status_event("正在初始化 Stronghold 服务...").await;

        match self.perform_initialization().await {
            Ok(()) => {
                self.status = InitializationStatus::Initialized;
                self.last_error = None;
                log::info!("Stronghold 初始化成功完成");
                self.emit_status_event("Stronghold 服务初始化成功").await;
                Ok(())
            }
            Err(e) => {
                let error_msg = format!("Stronghold 初始化失败: {}", e);
                log::error!("{}", error_msg);
                
                self.status = InitializationStatus::Failed(error_msg.clone());
                self.last_error = Some(error_msg.clone());
                
                // 发送错误事件
                self.emit_error_event(&error_msg).await;
                
                // 如果还有重试机会，标记为可重试
                if self.initialization_attempts < self.max_retry_attempts {
                    log::info!("将在稍后重试初始化 ({}/{})", 
                               self.initialization_attempts, self.max_retry_attempts);
                    self.status = InitializationStatus::NotInitialized;
                }
                
                Err(e)
            }
        }
    }

    /// 执行实际的初始化逻辑
    async fn perform_initialization(&mut self) -> StrongholdResult<()> {
        // 1. 检查 AppHandle 是否可用
        let app_handle = match &self.app_handle {
            Some(handle) => handle.clone(),
            None => {
                self.status = InitializationStatus::WaitingForAppHandle;
                return Err(StrongholdError::InitializationFailed(
                    "AppHandle 未设置，等待应用初始化完成".to_string()
                ));
            }
        };

        // 2. 初始化密码提供器（如果还没有有效密码）
        log::info!("步骤 1: 初始化密码提供器");
        
        // 检查密码提供器是否已经有有效密码
        let password_already_valid = validate_backend_password("Stronghold").await
            .unwrap_or(false);
        
        if !password_already_valid {
            log::info!("密码提供器需要初始化或重新设置密码");
            initialize_password_provider(self.is_production).await
                .map_err(|e| StrongholdError::InitializationFailed(
                    format!("密码提供器初始化失败: {}", e)
                ))?;
        } else {
            log::info!("密码提供器已有有效密码，跳过重新初始化");
        }

        // 3. 验证密码可用性
        log::info!("步骤 2: 验证密码可用性");
        let password_valid = validate_backend_password("Stronghold").await
            .unwrap_or(false);
        
        if !password_valid {
            return Err(StrongholdError::ConfigValidationFailed(
                "Stronghold 密码验证失败".to_string()
            ));
        }

        // 4. 创建 Stronghold 配置
        log::info!("步骤 3: 准备 Stronghold 配置");
        let stronghold_config = if self.is_production {
            create_default_config()
        } else {
            create_fast_config()
        };

        // 5. 创建统一存储适配器配置
        log::info!("步骤 4: 创建统一存储适配器");
        let adapter_config = AdapterConfig {
            primary_backend: BackendType::Stronghold,
            enable_stronghold: true,
            enable_keychain: true, // 作为备选方案
            failover_strategy: if self.is_production {
                FailoverStrategy::Smart
            } else {
                FailoverStrategy::Automatic
            },
            stronghold_config: Some(stronghold_config.clone()),
            health_check_interval: Duration::from_secs(if self.is_production { 300 } else { 60 }),
            max_retry_attempts: 3,
            retry_delay: Duration::from_millis(200),
        };

        // 6. 创建统一存储适配器
        let mut adapter = UnifiedStorageAdapter::new(adapter_config).await
            .map_err(|e| StrongholdError::InitializationFailed(
                format!("统一存储适配器创建失败: {}", e)
            ))?;

        // 7. 创建 Stronghold 管理器并启用后端
        log::info!("步骤 5: 创建 Stronghold 管理器并启用后端");
        let stronghold_manager = StrongholdManager::new(app_handle.clone(), stronghold_config.clone())
            .map_err(|e| StrongholdError::InitializationFailed(
                format!("Stronghold 管理器创建失败: {}", e)
            ))?;

        // 保存管理器引用
        self.stronghold_manager = Some(Arc::new(RwLock::new(stronghold_manager)));
        
        // 创建另一个管理器实例用于启用后端
        let stronghold_manager_for_adapter = StrongholdManager::new(app_handle.clone(), stronghold_config)
            .map_err(|e| StrongholdError::InitializationFailed(
                format!("为适配器创建 Stronghold 管理器失败: {}", e)
            ))?;
        
        adapter.enable_stronghold_backend(stronghold_manager_for_adapter).await
            .map_err(|e| StrongholdError::InitializationFailed(
                format!("启用 Stronghold 后端失败: {}", e)
            ))?;

        // 8. 执行一致性检查
        log::info!("步骤 6: 执行一致性检查");
        let consistency_issues = adapter.check_backend_consistency().await;
        if !consistency_issues.is_empty() {
            log::warn!("发现 {} 个一致性问题:", consistency_issues.len());
            for issue in &consistency_issues {
                log::warn!("  - {}", issue);
            }
            
            // 如果有严重问题，返回错误
            if consistency_issues.iter().any(|issue| 
                issue.contains("没有可用的后端") || 
                issue.contains("密码验证失败")
            ) {
                return Err(StrongholdError::ConfigValidationFailed(
                    format!("一致性检查失败: {}", consistency_issues.join(", "))
                ));
            }
        }

        // 9. 保存适配器实例
        self.adapter = Some(Arc::new(RwLock::new(adapter)));

        log::info!("Stronghold 完整初始化流程成功完成");
        Ok(())
    }

    /// 重试初始化
    /// 
    /// # 返回
    /// * `StrongholdResult<()>` - 重试结果
    pub async fn retry_initialization(&mut self) -> StrongholdResult<()> {
        if self.initialization_attempts >= self.max_retry_attempts {
            return Err(StrongholdError::InitializationFailed(
                format!("已达到最大重试次数 ({})", self.max_retry_attempts)
            ));
        }

        log::info!("重试 Stronghold 初始化");
        self.status = InitializationStatus::NotInitialized;
        self.initialize().await
    }

    /// 获取统一存储适配器
    /// 
    /// # 返回
    /// * `Option<Arc<RwLock<UnifiedStorageAdapter>>>` - 适配器实例
    pub fn get_adapter(&self) -> Option<Arc<RwLock<UnifiedStorageAdapter>>> {
        self.adapter.clone()
    }

    /// 获取 Stronghold 管理器
    /// 
    /// # 返回
    /// * `Option<Arc<RwLock<StrongholdManager>>>` - 管理器实例
    pub fn get_stronghold_manager(&self) -> Option<Arc<RwLock<StrongholdManager>>> {
        self.stronghold_manager.clone()
    }

    /// 获取初始化状态
    /// 
    /// # 返回
    /// * `InitializationStatus` - 当前状态
    pub fn get_status(&self) -> InitializationStatus {
        self.status.clone()
    }

    /// 获取最后的错误信息
    /// 
    /// # 返回
    /// * `Option<String>` - 错误信息
    pub fn get_last_error(&self) -> Option<String> {
        self.last_error.clone()
    }

    /// 检查是否已初始化
    /// 
    /// # 返回
    /// * `bool` - 是否已初始化
    pub fn is_initialized(&self) -> bool {
        matches!(self.status, InitializationStatus::Initialized)
    }

    /// 检查是否可以重试
    /// 
    /// # 返回
    /// * `bool` - 是否可以重试
    pub fn can_retry(&self) -> bool {
        self.initialization_attempts < self.max_retry_attempts &&
        !matches!(self.status, InitializationStatus::Initializing)
    }

    /// 重置初始化服务
    pub async fn reset(&mut self) {
        log::info!("重置 Stronghold 初始化服务");
        
        self.status = InitializationStatus::NotInitialized;
        self.adapter = None;
        self.stronghold_manager = None;
        self.last_error = None;
        self.initialization_attempts = 0;
        
        self.emit_status_event("Stronghold 服务已重置").await;
    }

    /// 发送状态事件到前端
    async fn emit_status_event(&self, message: &str) {
        if let Some(app_handle) = &self.app_handle {
            if let Some(window) = app_handle.get_webview_window("main") {
                let _ = window.emit("stronghold_initialization_status", serde_json::json!({
                    "status": format!("{:?}", self.status),
                    "message": message,
                    "attempts": self.initialization_attempts,
                    "max_attempts": self.max_retry_attempts,
                    "can_retry": self.can_retry(),
                    "is_production": self.is_production,
                }));
            }
        }
    }

    /// 发送错误事件到前端
    async fn emit_error_event(&self, error: &str) {
        if let Some(app_handle) = &self.app_handle {
            if let Some(window) = app_handle.get_webview_window("main") {
                let _ = window.emit("stronghold_initialization_error", serde_json::json!({
                    "error": error,
                    "attempts": self.initialization_attempts,
                    "max_attempts": self.max_retry_attempts,
                    "can_retry": self.can_retry(),
                }));
            }
        }
    }
}

/// 获取全局 Stronghold 初始化服务实例
/// 
/// # 返回
/// * `Arc<RwLock<StrongholdInitializationService>>` - 服务实例
pub fn get_global_stronghold_init_service() -> Arc<RwLock<StrongholdInitializationService>> {
    STRONGHOLD_INIT_SERVICE.get_or_init(|| {
        Arc::new(RwLock::new(StrongholdInitializationService::new()))
    }).clone()
}

/// 初始化全局 Stronghold 服务
/// 
/// 这个函数应该在应用启动时调用，在有 AppHandle 的情况下
/// 
/// # 参数
/// * `app_handle` - Tauri 应用句柄
/// 
/// # 返回
/// * `StrongholdResult<()>` - 初始化结果
pub async fn initialize_global_stronghold_service(app_handle: AppHandle) -> StrongholdResult<()> {
    log::info!("初始化全局 Stronghold 服务");
    
    let service = get_global_stronghold_init_service();
    let mut service = service.write().await;
    
    // 设置 AppHandle
    service.set_app_handle(app_handle).await?;
    
    // 开始初始化
    service.initialize().await
}

/// 等待全局 Stronghold 服务初始化完成
/// 
/// 这个函数会等待 Stronghold 服务完成初始化，或者超时失败
/// 
/// # 参数
/// * `timeout_seconds` - 超时时间（秒）
/// 
/// # 返回
/// * `StrongholdResult<()>` - 等待结果
pub async fn wait_for_global_stronghold_initialization(timeout_seconds: u64) -> StrongholdResult<()> {
    use tokio::time::{sleep, Duration, timeout};
    
    log::info!("等待全局 Stronghold 服务初始化完成，超时时间: {} 秒", timeout_seconds);
    
    let wait_future = async {
        loop {
            let status = get_global_stronghold_status().await;
            
            match status {
                InitializationStatus::Initialized => {
                    log::info!("Stronghold 服务初始化完成");
                    return Ok(());
                }
                InitializationStatus::Failed(error) => {
                    log::error!("Stronghold 服务初始化失败: {}", error);
                    return Err(StrongholdError::InitializationFailed(error));
                }
                InitializationStatus::NotInitialized => {
                    log::warn!("Stronghold 服务尚未开始初始化");
                    return Err(StrongholdError::InitializationFailed(
                        "Stronghold 服务尚未开始初始化".to_string()
                    ));
                }
                InitializationStatus::Initializing => {
                    log::debug!("Stronghold 服务正在初始化中，继续等待...");
                    sleep(Duration::from_millis(100)).await;
                }
                InitializationStatus::PasswordProviderNotReady => {
                    log::warn!("Stronghold 密码提供器未就绪");
                    return Err(StrongholdError::ConfigValidationFailed(
                        "Stronghold 密码提供器未就绪".to_string()
                    ));
                }
                InitializationStatus::WaitingForAppHandle => {
                    log::debug!("Stronghold 服务等待 AppHandle，继续等待...");
                    sleep(Duration::from_millis(100)).await;
                }
            }
        }
    };
    
    // 设置超时
    match timeout(Duration::from_secs(timeout_seconds), wait_future).await {
        Ok(result) => result,
        Err(_) => {
            log::error!("等待 Stronghold 服务初始化超时 ({} 秒)", timeout_seconds);
            Err(StrongholdError::InitializationFailed(
                format!("等待 Stronghold 服务初始化超时 ({} 秒)", timeout_seconds)
            ))
        }
    }
}

/// 获取全局统一存储适配器
/// 
/// # 返回
/// * `Option<Arc<RwLock<UnifiedStorageAdapter>>>` - 适配器实例
pub async fn get_global_unified_adapter() -> Option<Arc<RwLock<UnifiedStorageAdapter>>> {
    let service = get_global_stronghold_init_service();
    let service = service.read().await;
    service.get_adapter()
}

/// 获取全局 Stronghold 管理器
/// 
/// # 返回
/// * `Option<Arc<RwLock<StrongholdManager>>>` - 管理器实例
pub async fn get_global_stronghold_manager() -> Option<Arc<RwLock<StrongholdManager>>> {
    let service = get_global_stronghold_init_service();
    let service = service.read().await;
    service.get_stronghold_manager()
}

/// 检查全局 Stronghold 服务状态
/// 
/// # 返回
/// * `InitializationStatus` - 当前状态
pub async fn get_global_stronghold_status() -> InitializationStatus {
    let service = get_global_stronghold_init_service();
    let service = service.read().await;
    service.get_status()
}

/// 重试全局 Stronghold 初始化
/// 
/// # 返回
/// * `StrongholdResult<()>` - 重试结果
pub async fn retry_global_stronghold_initialization() -> StrongholdResult<()> {
    let service = get_global_stronghold_init_service();
    let mut service = service.write().await;
    service.retry_initialization().await
}

/// 重置全局 Stronghold 服务
/// 
/// # 返回
/// * `StrongholdResult<()>` - 重置结果
pub async fn reset_global_stronghold_service() -> StrongholdResult<()> {
    let service = get_global_stronghold_init_service();
    let mut service = service.write().await;
    service.reset().await;
    Ok(())
}

/// 增强的 Stronghold 可用性检测结果
#[derive(Debug, Clone, PartialEq)]
pub struct StrongholdAvailabilityStatus {
    /// 自定义初始化服务状态
    pub custom_service_status: InitializationStatus,
    /// Tauri 插件状态
    pub plugin_status: PluginStatus,
    /// 实际可读写测试结果
    pub read_write_test: ReadWriteTestResult,
    /// 综合可用性
    pub overall_available: bool,
    /// 错误信息
    pub error_details: Vec<String>,
}

/// Tauri 插件状态
#[derive(Debug, Clone, PartialEq)]
pub enum PluginStatus {
    /// 插件已注册并可用
    Available,
    /// 插件已注册但不可用
    RegisteredButUnavailable,
    /// 插件未注册
    NotRegistered,
    /// 检测失败
    DetectionFailed(String),
}

/// 读写测试结果
#[derive(Debug, Clone, PartialEq)]
pub enum ReadWriteTestResult {
    /// 测试成功
    Success,
    /// 写入失败
    WriteFailed(String),
    /// 读取失败
    ReadFailed(String),
    /// 数据不一致
    DataInconsistent,
    /// 测试跳过（插件不可用）
    Skipped,
}

/// 增强的 Stronghold 可用性检测
/// 
/// 这个函数综合检测：
/// 1. 自定义初始化服务的状态
/// 2. tauri-plugin-stronghold 的实际可用性
/// 3. 通过实际读写操作测试功能性
/// 
/// # 参数
/// * `app_handle` - Tauri 应用句柄（可选）
/// 
/// # 返回
/// * `StrongholdAvailabilityStatus` - 详细的可用性状态
pub async fn check_enhanced_stronghold_availability(
    app_handle: Option<AppHandle>
) -> StrongholdAvailabilityStatus {
    let mut error_details = Vec::new();
    
    // 1. 检查自定义初始化服务状态
    let custom_service_status = get_global_stronghold_status().await;
    log::info!("自定义初始化服务状态: {:?}", custom_service_status);
    
    // 2. 检查 Tauri 插件状态
    let plugin_status = check_tauri_plugin_stronghold_status(app_handle.as_ref()).await;
    log::info!("Tauri 插件状态: {:?}", plugin_status);
    
    // 3. 执行实际读写测试
    let read_write_test = if matches!(plugin_status, PluginStatus::Available) {
        perform_stronghold_read_write_test(app_handle.as_ref()).await
    } else {
        error_details.push("跳过读写测试：Tauri 插件不可用".to_string());
        ReadWriteTestResult::Skipped
    };
    
    log::info!("读写测试结果: {:?}", read_write_test);
    
    // 4. 确定综合可用性
    let overall_available = determine_overall_availability(
        &custom_service_status,
        &plugin_status,
        &read_write_test,
        &mut error_details
    );
    
    StrongholdAvailabilityStatus {
        custom_service_status,
        plugin_status,
        read_write_test,
        overall_available,
        error_details,
    }
}

/// 检查 tauri-plugin-stronghold 的状态
async fn check_tauri_plugin_stronghold_status(
    app_handle: Option<&AppHandle>
) -> PluginStatus {
    let app_handle = match app_handle {
        Some(handle) => handle,
        None => {
            log::warn!("无 AppHandle，无法检测 Tauri 插件状态");
            return PluginStatus::DetectionFailed("缺少 AppHandle".to_string());
        }
    };

    // 尝试通过调用 Stronghold 插件的 API 来检测插件状态
    // 这是更可靠的检测方法，因为它直接测试插件功能
    match verify_stronghold_plugin_functionality(app_handle).await {
        Ok(true) => {
            log::info!("Stronghold 插件功能验证成功");
            PluginStatus::Available
        }
        Ok(false) => {
            log::warn!("Stronghold 插件已注册但功能不可用");
            PluginStatus::RegisteredButUnavailable
        }
        Err(e) => {
            // 如果验证失败，可能是插件未注册或有其他问题
            if e.contains("not found") || e.contains("未注册") {
                log::warn!("未检测到 Stronghold 插件状态，插件可能未注册");
                PluginStatus::NotRegistered
            } else {
                log::error!("Stronghold 插件功能验证失败: {}", e);
                PluginStatus::DetectionFailed(e.to_string())
            }
        }
    }
}

/// 验证 Stronghold 插件功能性
async fn verify_stronghold_plugin_functionality(
    app_handle: &AppHandle
) -> Result<bool, String> {
    // 尝试通过检测 Stronghold 插件状态来验证插件功能
    // 由于 Tauri 2.x 的 AppHandle 没有直接的 plugins() 方法，
    // 我们使用状态检查作为主要的检测方法

    // 方法1：尝试访问插件状态
    match app_handle.try_state::<tauri_plugin_stronghold::stronghold::Stronghold>() {
        Some(_stronghold_state) => {
            log::info!("Stronghold 插件状态可访问，插件已正确注册");
            Ok(true)
        }
        None => {
            // 在某些情况下，插件可能已注册但状态不可直接访问
            // 这可能是正常的，取决于插件的实现方式
            log::info!("Stronghold 插件状态不可直接访问");

            // 方法2：尝试通过其他方式验证插件存在
            // 由于我们在 lib.rs 中已经注册了插件，我们可以假设它是可用的
            // 但我们仍然返回 false 以表示无法直接验证
            Err("无法直接验证 Stronghold 插件状态".to_string())
        }
    }
}

/// 执行 Stronghold 读写测试
async fn perform_stronghold_read_write_test(
    app_handle: Option<&AppHandle>
) -> ReadWriteTestResult {
    let _app_handle = match app_handle {
        Some(handle) => handle,
        None => return ReadWriteTestResult::Skipped,
    };
    
    // 使用测试键值对
    let test_key = "stronghold_availability_test";
    let test_value = format!("test_data_{}", chrono::Utc::now().timestamp()).into_bytes();
    
    // 尝试通过自定义管理器进行读写测试
    match get_global_stronghold_manager().await {
        Some(manager) => {
            // 写入测试
            {
                let manager = manager.read().await;
                match manager.store(test_key, &test_value, "test_data").await {
                    Ok(_) => {
                        log::info!("Stronghold 写入测试成功");
                    }
                    Err(e) => {
                        log::error!("Stronghold 写入测试失败: {}", e);
                        return ReadWriteTestResult::WriteFailed(e.to_string());
                    }
                }
            }
            
            // 读取测试
            {
                let manager = manager.read().await;
                match manager.get(test_key).await {
                    Ok(Some(retrieved_item)) => {
                        match retrieved_item.decode_data() {
                            Ok(retrieved_data) => {
                                if retrieved_data == test_value {
                                    log::info!("Stronghold 读取测试成功，数据一致");
                                    
                                    // 清理测试数据
                                    let _ = manager.remove(test_key).await;
                                    
                                    ReadWriteTestResult::Success
                                } else {
                                    log::error!("Stronghold 读取测试失败：数据不一致");
                                    ReadWriteTestResult::DataInconsistent
                                }
                            }
                            Err(e) => {
                                log::error!("解码数据失败: {}", e);
                                ReadWriteTestResult::ReadFailed(format!("解码失败: {}", e))
                            }
                        }
                    }
                    Ok(None) => {
                        log::error!("Stronghold 未找到测试数据");
                        ReadWriteTestResult::ReadFailed("未找到数据".to_string())
                    }
                    Err(e) => {
                        log::error!("Stronghold 读取测试失败: {}", e);
                        ReadWriteTestResult::ReadFailed(e.to_string())
                    }
                }
            }
        }
        None => {
            log::warn!("无法获取 Stronghold 管理器，跳过读写测试");
            ReadWriteTestResult::Skipped
        }
    }
}

/// 确定综合可用性状态
fn determine_overall_availability(
    custom_service_status: &InitializationStatus,
    plugin_status: &PluginStatus,
    read_write_test: &ReadWriteTestResult,
    error_details: &mut Vec<String>
) -> bool {
    let mut available = true;
    
    // 检查自定义服务状态
    match custom_service_status {
        InitializationStatus::Initialized => {
            log::info!("✓ 自定义初始化服务已完成");
        }
        InitializationStatus::Initializing => {
            error_details.push("自定义初始化服务仍在初始化中".to_string());
            available = false;
        }
        InitializationStatus::Failed(err) => {
            error_details.push(format!("自定义初始化服务失败: {}", err));
            available = false;
        }
        _ => {
            error_details.push("自定义初始化服务未准备就绪".to_string());
            available = false;
        }
    }
    
    // 检查插件状态
    match plugin_status {
        PluginStatus::Available => {
            log::info!("✓ Tauri Stronghold 插件可用");
        }
        PluginStatus::RegisteredButUnavailable => {
            error_details.push("Tauri Stronghold 插件已注册但不可用".to_string());
            available = false;
        }
        PluginStatus::NotRegistered => {
            error_details.push("Tauri Stronghold 插件未注册".to_string());
            available = false;
        }
        PluginStatus::DetectionFailed(err) => {
            error_details.push(format!("Tauri Stronghold 插件检测失败: {}", err));
            available = false;
        }
    }
    
    // 检查读写测试结果
    match read_write_test {
        ReadWriteTestResult::Success => {
            log::info!("✓ Stronghold 读写功能测试通过");
        }
        ReadWriteTestResult::WriteFailed(err) => {
            error_details.push(format!("Stronghold 写入功能失败: {}", err));
            available = false;
        }
        ReadWriteTestResult::ReadFailed(err) => {
            error_details.push(format!("Stronghold 读取功能失败: {}", err));
            available = false;
        }
        ReadWriteTestResult::DataInconsistent => {
            error_details.push("Stronghold 数据一致性测试失败".to_string());
            available = false;
        }
        ReadWriteTestResult::Skipped => {
            log::info!("⚠ Stronghold 读写测试被跳过");
            // 跳过测试不影响可用性判断，如果其他条件满足
        }
    }
    
    available
}

/// 等待 Stronghold 完全可用
/// 
/// 这个函数会持续检查直到 Stronghold 完全可用或超时
/// 
/// # 参数
/// * `app_handle` - Tauri 应用句柄
/// * `timeout_seconds` - 超时时间（秒）
/// * `check_interval_ms` - 检查间隔（毫秒）
/// 
/// # 返回
/// * `Result<StrongholdAvailabilityStatus, String>` - 最终状态或超时错误
pub async fn wait_for_stronghold_fully_available(
    app_handle: AppHandle,
    timeout_seconds: u64,
    check_interval_ms: u64,
) -> Result<StrongholdAvailabilityStatus, String> {
    use tokio::time::{sleep, Duration, Instant};
    
    let start_time = Instant::now();
    let timeout_duration = Duration::from_secs(timeout_seconds);
    let check_interval = Duration::from_millis(check_interval_ms);
    
    log::info!("开始等待 Stronghold 完全可用（超时: {}秒）", timeout_seconds);
    
    loop {
        let status = check_enhanced_stronghold_availability(Some(app_handle.clone())).await;
        
        if status.overall_available {
            log::info!("Stronghold 已完全可用");
            return Ok(status);
        }
        
        // 检查是否超时
        if start_time.elapsed() >= timeout_duration {
            let error_msg = format!(
                "等待 Stronghold 可用超时（{}秒）。最终状态: {:?}，错误: {:?}",
                timeout_seconds,
                status.overall_available,
                status.error_details
            );
            log::error!("{}", error_msg);
            return Err(error_msg);
        }
        
        // 等待后重试
        sleep(check_interval).await;
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_initialization_service_creation() {
        let service = StrongholdInitializationService::new();
        assert_eq!(service.get_status(), InitializationStatus::NotInitialized);
        assert!(service.get_last_error().is_none());
        assert!(service.can_retry());
        assert!(!service.is_initialized());
    }

    #[tokio::test]
    async fn test_global_service_access() {
        let service1 = get_global_stronghold_init_service();
        let service2 = get_global_stronghold_init_service();
        
        // 确保返回的是同一个实例
        assert!(Arc::ptr_eq(&service1, &service2));
    }

    #[tokio::test]
    async fn test_initialization_status_transitions() {
        let mut service = StrongholdInitializationService::new();
        
        // 初始状态
        assert_eq!(service.get_status(), InitializationStatus::NotInitialized);
        
        // 没有 AppHandle 时应该返回等待状态
        let result = service.initialize().await;
        assert!(result.is_err());
        // 修改：现在初始化失败后会重置为 NotInitialized（如果还有重试机会）
        // 或者变为 Failed 状态
        let status = service.get_status();
        assert!(matches!(status, InitializationStatus::NotInitialized | InitializationStatus::Failed(_)));
    }

    #[tokio::test]
    async fn test_retry_logic() {
        let mut service = StrongholdInitializationService::new();
        service.max_retry_attempts = 2;
        
        // 第一次尝试失败
        let _ = service.initialize().await;
        assert!(service.can_retry());
        assert_eq!(service.initialization_attempts, 1);
        
        // 第二次尝试失败
        let _ = service.retry_initialization().await;
        assert_eq!(service.initialization_attempts, 2);
        
        // 达到最大重试次数后不能再重试
        let result = service.retry_initialization().await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_service_reset() {
        let mut service = StrongholdInitializationService::new();
        service.initialization_attempts = 2;
        service.last_error = Some("test error".to_string());
        
        service.reset().await;
        
        assert_eq!(service.get_status(), InitializationStatus::NotInitialized);
        assert_eq!(service.initialization_attempts, 0);
        assert!(service.get_last_error().is_none());
    }
} 