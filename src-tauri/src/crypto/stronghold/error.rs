// Stronghold 错误处理模块
// 定义 Stronghold 相关的错误类型和结果类型

use crate::errors::VaultError;
use thiserror::Error;
use serde::{Deserialize, Serialize};

/// Stronghold 操作结果类型
pub type StrongholdResult<T> = Result<T, StrongholdError>;

/// Stronghold 错误类型
#[derive(Error, Debug)]
pub enum StrongholdError {
    #[error("Stronghold 初始化失败: {0}")]
    InitializationFailed(String),

    #[error("Stronghold 未初始化")]
    NotInitialized,

    #[error("Stronghold 客户端创建失败: {0}")]
    ClientCreationFailed(String),

    #[error("Stronghold 存储操作失败: {0}")]
    StorageOperationFailed(String),

    #[error("Stronghold 密钥未找到: {key}")]
    KeyNotFound { key: String },

    #[error("Stronghold 密钥已存在: {key}")]
    KeyAlreadyExists { key: String },

    #[error("Stronghold 配置无效: {0}")]
    InvalidConfiguration(String),

    #[error("Stronghold 超时: 操作在 {timeout_ms}ms 内未完成")]
    Timeout { timeout_ms: u64 },

    #[error("Stronghold 访问被拒绝: {0}")]
    AccessDenied(String),

    #[error("Stronghold 数据损坏: {0}")]
    DataCorruption(String),

    #[error("Stronghold 版本不兼容: 期望 {expected}, 实际 {actual}")]
    VersionMismatch { expected: String, actual: String },

    #[error("Stronghold 序列化错误: {0}")]
    SerializationError(String),

    #[error("Stronghold 反序列化错误: {0}")]
    DeserializationError(String),

    #[error("Stronghold 文件系统错误: {0}")]
    FileSystemError(String),

    #[error("Stronghold 网络错误: {0}")]
    NetworkError(String),

    #[error("Stronghold 内部错误: {0}")]
    InternalError(String),

    #[error("后端不可用: {backend}")]
    BackendUnavailable { backend: String },

    #[error("故障转移失败: 所有后端都不可用")]
    FailoverFailed,

    #[error("健康检查失败: {backend} - {reason}")]
    HealthCheckFailed { backend: String, reason: String },

    #[error("配置验证失败: {0}")]
    ConfigValidationFailed(String),

    #[error("操作被取消")]
    OperationCancelled,

    #[error("重试次数超限: 已重试 {attempts} 次")]
    RetryLimitExceeded { attempts: u32 },

    #[error("Tauri 插件错误: {0}")]
    TauriPluginError(String),

    #[error("JSON 错误: {0}")]
    JsonError(String),

    #[error("Base64 编码错误: {0}")]
    Base64Error(String),

    #[error("密钥派生错误: {0}")]
    KeyDerivationError(String),

    #[error("加密错误: {0}")]
    EncryptionError(String),

    #[error("解密错误: {0}")]
    DecryptionError(String),

    #[error("操作失败: {0}")]
    OperationFailed(String),

    #[error("无效数据: {0}")]
    InvalidData(String),

    #[error("后端错误: {0}")]
    BackendError(String),

    #[error("没有可用的后端")]
    NoBackendAvailable,

    #[error("超时错误: {0}")]
    TimeoutError(String),

    #[error("连接失败: {0}")]
    ConnectionFailed(String),
}

impl From<StrongholdError> for VaultError {
    fn from(err: StrongholdError) -> Self {
        match err {
            StrongholdError::KeyNotFound { key } => {
                VaultError::InternalError(format!("Stronghold 密钥未找到: {}", key))
            }
            StrongholdError::AccessDenied(msg) => {
                VaultError::AuthenticationError(format!("Stronghold 访问被拒绝: {}", msg))
            }
            StrongholdError::Timeout { timeout_ms } => {
                VaultError::InternalError(format!("Stronghold 操作超时: {}ms", timeout_ms))
            }
            StrongholdError::EncryptionError(msg) => {
                VaultError::Encryption(format!("Stronghold 加密错误: {}", msg))
            }
            StrongholdError::DecryptionError(msg) => {
                VaultError::Decryption(format!("Stronghold 解密错误: {}", msg))
            }
            _ => VaultError::InternalError(format!("Stronghold 错误: {}", err)),
        }
    }
}

impl From<serde_json::Error> for StrongholdError {
    fn from(err: serde_json::Error) -> Self {
        StrongholdError::JsonError(err.to_string())
    }
}

impl From<base64::DecodeError> for StrongholdError {
    fn from(err: base64::DecodeError) -> Self {
        StrongholdError::Base64Error(err.to_string())
    }
}

impl From<std::io::Error> for StrongholdError {
    fn from(err: std::io::Error) -> Self {
        StrongholdError::FileSystemError(err.to_string())
    }
}

/// 错误上下文扩展 trait
pub trait StrongholdErrorExt<T> {
    /// 添加上下文信息到错误
    fn with_context(self, context: &str) -> StrongholdResult<T>;
    
    /// 添加操作上下文
    fn with_operation(self, operation: &str) -> StrongholdResult<T>;
    
    /// 添加密钥上下文
    fn with_key(self, key: &str) -> StrongholdResult<T>;
}

impl<T> StrongholdErrorExt<T> for StrongholdResult<T> {
    fn with_context(self, context: &str) -> StrongholdResult<T> {
        self.map_err(|e| {
            StrongholdError::InternalError(format!("{}: {}", context, e))
        })
    }
    
    fn with_operation(self, operation: &str) -> StrongholdResult<T> {
        self.with_context(&format!("操作 '{}' 失败", operation))
    }
    
    fn with_key(self, key: &str) -> StrongholdResult<T> {
        self.with_context(&format!("密钥 '{}' 操作失败", key))
    }
}

/// 重试策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RetryStrategy {
    /// 最大重试次数
    pub max_attempts: u32,
    /// 初始延迟（毫秒）
    pub initial_delay_ms: u64,
    /// 延迟倍增因子
    pub backoff_multiplier: f64,
    /// 最大延迟（毫秒）
    pub max_delay_ms: u64,
}

impl Default for RetryStrategy {
    fn default() -> Self {
        Self {
            max_attempts: 3,
            initial_delay_ms: 100,
            backoff_multiplier: 2.0,
            max_delay_ms: 5000,
        }
    }
}

impl RetryStrategy {
    /// 创建快速重试策略
    pub fn fast() -> Self {
        Self {
            max_attempts: 2,
            initial_delay_ms: 50,
            backoff_multiplier: 1.5,
            max_delay_ms: 1000,
        }
    }
    
    /// 创建保守重试策略
    pub fn conservative() -> Self {
        Self {
            max_attempts: 5,
            initial_delay_ms: 200,
            backoff_multiplier: 2.5,
            max_delay_ms: 10000,
        }
    }
    
    /// 计算指定尝试次数的延迟时间
    pub fn delay_for_attempt(&self, attempt: u32) -> u64 {
        if attempt == 0 {
            return 0;
        }
        
        let delay = self.initial_delay_ms as f64 * self.backoff_multiplier.powi(attempt as i32 - 1);
        (delay as u64).min(self.max_delay_ms)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_conversion() {
        let stronghold_error = StrongholdError::KeyNotFound {
            key: "test_key".to_string(),
        };
        
        let vault_error: VaultError = stronghold_error.into();
        assert!(matches!(vault_error, VaultError::InternalError(_)));
    }

    #[test]
    fn test_error_context() {
        let result: StrongholdResult<()> = Err(StrongholdError::NotInitialized);
        let with_context = result.with_context("测试操作");
        
        assert!(with_context.is_err());
        let error_msg = with_context.unwrap_err().to_string();
        assert!(error_msg.contains("测试操作"));
    }

    #[test]
    fn test_retry_strategy() {
        let strategy = RetryStrategy::default();
        
        assert_eq!(strategy.delay_for_attempt(0), 0);
        assert_eq!(strategy.delay_for_attempt(1), 100);
        assert_eq!(strategy.delay_for_attempt(2), 200);
        assert_eq!(strategy.delay_for_attempt(3), 400);
        
        // 测试最大延迟限制
        let delay = strategy.delay_for_attempt(10);
        assert!(delay <= strategy.max_delay_ms);
    }

    #[test]
    fn test_retry_strategy_presets() {
        let fast = RetryStrategy::fast();
        assert_eq!(fast.max_attempts, 2);
        assert_eq!(fast.initial_delay_ms, 50);
        
        let conservative = RetryStrategy::conservative();
        assert_eq!(conservative.max_attempts, 5);
        assert_eq!(conservative.initial_delay_ms, 200);
    }
} 