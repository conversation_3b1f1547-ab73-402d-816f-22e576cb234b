// Stronghold 管理器模块
// 负责与 tauri-plugin-stronghold 插件交互，提供核心存储功能

use std::collections::HashMap;
use std::time::{SystemTime, UNIX_EPOCH};

use log::{debug, info};
use serde::{Deserialize, Serialize};
use serde_json;
use tauri::{<PERSON><PERSON><PERSON><PERSON><PERSON>, Manager};
use tauri_plugin_stronghold::stronghold::Stronghold;
use iota_stronghold::SnapshotPath;

use super::{
    config::StrongholdConfig,
    error::{StrongholdError, StrongholdResult},
};
use crate::crypto::{secure_memory::SecureBytes, KEY_SIZE};
use base64::{engine::general_purpose::STANDARD as BASE64_STANDARD, Engine as _};

/// Stronghold 存储的数据项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StrongholdItem {
    /// 数据内容（Base64编码）
    pub data: String,
    /// 数据类型标识
    pub item_type: String,
    /// 创建时间戳
    pub created_at: i64,
    /// 最后访问时间戳
    pub last_accessed: i64,
    /// 元数据
    pub metadata: HashMap<String, String>,
}

impl StrongholdItem {
    /// 创建新的存储项
    pub fn new(data: &[u8], item_type: &str) -> Self {
        let now = current_timestamp();
        Self {
            data: BASE64_STANDARD.encode(data),
            item_type: item_type.to_string(),
            created_at: now,
            last_accessed: now,
            metadata: HashMap::new(),
        }
    }
    
    /// 从字符串创建存储项
    pub fn from_string(data: &str, item_type: &str) -> Self {
        Self::new(data.as_bytes(), item_type)
    }
    
    /// 获取解码后的数据
    pub fn decode_data(&self) -> StrongholdResult<Vec<u8>> {
        BASE64_STANDARD
            .decode(&self.data)
            .map_err(StrongholdError::from)
    }
    
    /// 获取数据作为字符串
    pub fn as_string(&self) -> StrongholdResult<String> {
        let bytes = self.decode_data()?;
        String::from_utf8(bytes)
            .map_err(|e| StrongholdError::DeserializationError(e.to_string()))
    }
    
    /// 更新最后访问时间
    pub fn touch(&mut self) {
        self.last_accessed = current_timestamp();
    }
    
    /// 添加元数据
    pub fn add_metadata(&mut self, key: &str, value: &str) {
        self.metadata.insert(key.to_string(), value.to_string());
    }
    
    /// 获取元数据
    pub fn get_metadata(&self, key: &str) -> Option<&String> {
        self.metadata.get(key)
    }
}

/// Stronghold 客户端状态
#[derive(Debug, Clone, PartialEq)]
pub enum ClientState {
    /// 未初始化
    Uninitialized,
    /// 正在初始化
    Initializing,
    /// 已初始化
    Initialized,
    /// 错误状态
    Error(String),
}

/// Stronghold 管理器
#[derive(Debug)]
pub struct StrongholdManager {
    /// Tauri 应用句柄
    app_handle: AppHandle,
    /// 配置
    config: StrongholdConfig,
}

impl StrongholdManager {
    /// 创建新的 Stronghold 管理器
    pub fn new(app_handle: AppHandle, config: StrongholdConfig) -> StrongholdResult<Self> {
        // 验证配置
        config.validate()?;
        
        info!("创建 Stronghold 管理器，配置: {:?}", config);
        
        Ok(Self { app_handle, config })
    }
    
    /// 存储数据
    pub async fn store(&self, key: &str, data: &[u8], item_type: &str) -> StrongholdResult<()> {
        let item = StrongholdItem::new(data, item_type);
        let serialized = serde_json::to_string(&item)?;
        
        debug!("存储数据到 Stronghold: key={}, type={}", key, item_type);
        
        // 使用 try_state 来安全检查 Stronghold 是否可用
        let stronghold = self.app_handle.try_state::<Stronghold>()
            .ok_or_else(|| StrongholdError::NotInitialized)?;
        
        let client = stronghold.load_client(self.config.client_name.as_bytes()).map_err(|e| StrongholdError::ClientCreationFailed(e.to_string()))?;
        
        client
            .store()
            .insert(key.as_bytes().to_vec(), serialized.into_bytes(), None)
            .map_err(|e| StrongholdError::StorageOperationFailed(e.to_string()))?;
        
        self.commit_if_needed()
    }
    
    /// 存储密钥（特殊处理）
    pub async fn store_key(&self, key: &str, key_data: &[u8; KEY_SIZE]) -> StrongholdResult<()> {
        // 使用安全内存处理密钥
        let secure_data = SecureBytes::from_slice(key_data);
        self.store(key, secure_data.as_slice(), "encryption_key")
            .await
    }
    
    /// 获取数据
    pub async fn get(&self, key: &str) -> StrongholdResult<Option<StrongholdItem>> {
        debug!("从 Stronghold 获取数据: key={}", key);
        
        // 使用 try_state 来安全检查 Stronghold 是否可用
        let stronghold = self.app_handle.try_state::<Stronghold>()
            .ok_or_else(|| StrongholdError::NotInitialized)?;
        
        let client = stronghold.load_client(self.config.client_name.as_bytes()).map_err(|e| StrongholdError::ClientCreationFailed(e.to_string()))?;
        
        let result = client
            .store()
            .get(key.as_bytes())
            .map_err(|e| StrongholdError::StorageOperationFailed(e.to_string()))?;
        
        match result {
            Some(data) => Ok(Some(serde_json::from_slice(&data)?)),
            None => Ok(None),
        }
    }
    
    /// 获取密钥（特殊处理）
    pub async fn get_key(&self, key: &str) -> StrongholdResult<Option<[u8; KEY_SIZE]>> {
        match self.get(key).await? {
            Some(item) => {
                if item.item_type != "encryption_key" {
                    return Err(StrongholdError::InvalidConfiguration(
                        format!("密钥 {} 的类型不正确: {}", key, item.item_type),
                    ));
                }
                
                let data = item.decode_data()?;
                if data.len() != KEY_SIZE {
                    return Err(StrongholdError::InvalidConfiguration(
                        format!("密钥 {} 的长度不正确: {} (期望: {})", key, data.len(), KEY_SIZE),
                    ));
                }
                
                let mut key_array = [0u8; KEY_SIZE];
                key_array.copy_from_slice(&data);
                Ok(Some(key_array))
            }
            None => Ok(None),
        }
    }
    
    /// 删除数据
    pub async fn remove(&self, key: &str) -> StrongholdResult<bool> {
        debug!("从 Stronghold 删除数据: key={}", key);
        
        // 使用 try_state 来安全检查 Stronghold 是否可用
        let stronghold = self.app_handle.try_state::<Stronghold>()
            .ok_or_else(|| StrongholdError::NotInitialized)?;
        
        let client = stronghold.load_client(self.config.client_name.as_bytes()).map_err(|e| StrongholdError::ClientCreationFailed(e.to_string()))?;
        
        // 首先检查密钥是否存在
        if self.exists(key).await? {
            // 如果存在，则删除
            client.store().delete(key.as_bytes())
                .map_err(|e| StrongholdError::StorageOperationFailed(e.to_string()))?;
            self.commit_if_needed()?;
            Ok(true)
        } else {
            // 如果不存在，返回 false
            Ok(false)
        }
    }
    
    /// 列出所有密钥
    pub async fn list_keys(&self) -> StrongholdResult<Vec<String>> {
        debug!("列出 Stronghold 中的所有密钥");
        
        // 使用 try_state 来安全检查 Stronghold 是否可用
        let stronghold = self.app_handle.try_state::<Stronghold>()
            .ok_or_else(|| StrongholdError::NotInitialized)?;
        
        let client = stronghold.load_client(self.config.client_name.as_bytes()).map_err(|e| StrongholdError::ClientCreationFailed(e.to_string()))?;
        
        let keys_bytes = client
            .store()
            .keys()
            .map_err(|e| StrongholdError::StorageOperationFailed(e.to_string()))?;
        
        keys_bytes
            .into_iter()
            .map(|k| String::from_utf8(k).map_err(|e| StrongholdError::DeserializationError(e.to_string())))
            .collect::<Result<Vec<String>, _>>()
    }
    
    /// 检查密钥是否存在
    pub async fn exists(&self, key: &str) -> StrongholdResult<bool> {
        match self.get(key).await? {
            Some(_) => Ok(true),
            None => Ok(false),
        }
    }
    
    /// 创建快照
    pub fn create_snapshot(&self) -> StrongholdResult<()> {
        info!("创建 Stronghold 快照");
        
        // 使用 try_state 来安全检查 Stronghold 是否可用
        let stronghold = self.app_handle.try_state::<Stronghold>()
            .ok_or_else(|| StrongholdError::NotInitialized)?;
        
        let snapshot_path = SnapshotPath::from_path(&self.config.vault_path);
        
        stronghold
            .commit(&snapshot_path)
            .map_err(|e| StrongholdError::StorageOperationFailed(format!("快照失败: {}", e)))
    }
    
    /// 检查并创建快照
    fn commit_if_needed(&self) -> StrongholdResult<()> {
        if self.config.auto_save {
            self.create_snapshot()?;
        }
        Ok(())
    }
}

/// 获取当前时间戳
fn current_timestamp() -> i64 {
    SystemTime::now()
        .duration_since(UNIX_EPOCH)
        .unwrap_or_default()
        .as_secs() as i64
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::crypto::stronghold::config::create_fast_config;

    // 注意：这些测试需要 Tauri 环境，在单元测试中可能无法运行
    // 实际测试应该在集成测试中进行

    #[test]
    fn test_stronghold_item_creation() {
        let data = b"test data";
        let item = StrongholdItem::new(data, "test_type");
        
        assert_eq!(item.item_type, "test_type");
        assert_eq!(item.decode_data().unwrap(), data.to_vec());
        assert!(item.created_at > 0);
        assert_eq!(item.created_at, item.last_accessed);
    }

    #[test]
    fn test_stronghold_item_string_operations() {
        let text = "Hello, World!";
        let item = StrongholdItem::from_string(text, "text");
        
        assert_eq!(item.as_string().unwrap(), text);
        assert_eq!(item.item_type, "text");
    }

    #[test]
    fn test_stronghold_item_metadata() {
        let mut item = StrongholdItem::new(b"data", "type");
        
        item.add_metadata("key1", "value1");
        item.add_metadata("key2", "value2");
        
        assert_eq!(item.get_metadata("key1"), Some(&"value1".to_string()));
        assert_eq!(item.get_metadata("key2"), Some(&"value2".to_string()));
        assert_eq!(item.get_metadata("nonexistent"), None);
    }

    #[test]
    fn test_client_state() {
        assert_eq!(ClientState::Uninitialized, ClientState::Uninitialized);
        assert_ne!(ClientState::Uninitialized, ClientState::Initialized);
        
        let error_state = ClientState::Error("test error".to_string());
        match error_state {
            ClientState::Error(msg) => assert_eq!(msg, "test error"),
            _ => panic!("Expected error state"),
        }
    }
} 