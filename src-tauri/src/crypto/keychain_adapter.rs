/// 跨平台密钥链适配器
/// 
/// 提供统一的密钥存储接口，在桌面端使用系统密钥链，在移动端使用平台特定的安全存储

use crate::errors::{VaultResult, VaultError};
use crate::crypto::{KEY_SIZE, StrongholdFactory, AdapterConfig};
use log::warn;
use base64::{engine::general_purpose::STANDARD as BASE64_STANDARD, Engine as _};


/// 跨平台密钥链管理器
pub struct CrossPlatformKeychain {
    service_name: String,
    account_name: String,
}

impl CrossPlatformKeychain {
    /// 创建新的跨平台密钥链管理器
    pub fn new(service_name: &str, account_name: &str) -> Self {
        Self {
            service_name: service_name.to_string(),
            account_name: account_name.to_string(),
        }
    }
    
    /// 存储密钥
    pub async fn store_key(&self, key: &[u8; KEY_SIZE]) -> VaultResult<()> {
        let config = AdapterConfig::default();
        let factory = StrongholdFactory::default();
        let adapter = factory.create_unified_adapter(Some(config)).await
            .map_err(|e| VaultError::InternalError(format!("创建适配器失败: {}", e)))?;

        let storage_key = format!("{}:{}", self.service_name, self.account_name);
        adapter.store(&storage_key, key).await
            .map_err(|e| VaultError::InternalError(format!("存储失败: {}", e)))?;

        Ok(())
    }
    
    /// 获取密钥
    pub async fn get_key(&self) -> VaultResult<[u8; KEY_SIZE]> {
        let config = AdapterConfig::default();
        let factory = StrongholdFactory::default();
        let adapter = factory.create_unified_adapter(Some(config)).await
            .map_err(|e| VaultError::InternalError(format!("创建适配器失败: {}", e)))?;

        let storage_key = format!("{}:{}", self.service_name, self.account_name);
        let data = adapter.get(&storage_key).await
            .map_err(|e| VaultError::InternalError(format!("获取失败: {}", e)))?
            .ok_or_else(|| VaultError::NotFound(storage_key.clone()))?;

        if data.len() != KEY_SIZE {
            return Err(VaultError::InternalError(format!(
                "密钥长度不匹配，期望 {}，实际 {}",
                KEY_SIZE,
                data.len()
            )));
        }

        let mut key = [0u8; KEY_SIZE];
        key.copy_from_slice(&data);
        Ok(key)
    }
    
    /// 存储字符串数据
    pub async fn store_string(&self, data: &str) -> VaultResult<()> {
        let config = AdapterConfig::default();
        let factory = StrongholdFactory::default();
        let adapter = factory.create_unified_adapter(Some(config)).await
            .map_err(|e| VaultError::InternalError(format!("创建适配器失败: {}", e)))?;

        let storage_key = format!("{}:{}", self.service_name, self.account_name);
        adapter.store(&storage_key, data.as_bytes()).await
            .map_err(|e| VaultError::InternalError(format!("存储失败: {}", e)))?;

        Ok(())
    }
    
    /// 获取字符串数据
    pub async fn get_string(&self) -> VaultResult<String> {
        let config = AdapterConfig::default();
        let factory = StrongholdFactory::default();
        let adapter = factory.create_unified_adapter(Some(config)).await
            .map_err(|e| VaultError::InternalError(format!("创建适配器失败: {}", e)))?;

        let storage_key = format!("{}:{}", self.service_name, self.account_name);
        let data = adapter.get(&storage_key).await
            .map_err(|e| VaultError::InternalError(format!("获取失败: {}", e)))?
            .ok_or_else(|| VaultError::NotFound(storage_key.clone()))?;

        String::from_utf8(data)
            .map_err(|e| VaultError::InternalError(format!("UTF-8 解码失败: {}", e)))
    }
    
    /// 删除数据
    pub async fn delete(&self) -> VaultResult<()> {
        let config = AdapterConfig::default();
        let factory = StrongholdFactory::default();
        let adapter = factory.create_unified_adapter(Some(config)).await
            .map_err(|e| VaultError::InternalError(format!("创建适配器失败: {}", e)))?;

        let storage_key = format!("{}:{}", self.service_name, self.account_name);
        adapter.remove(&storage_key).await
            .map_err(|e| VaultError::InternalError(format!("删除失败: {}", e)))?;

        Ok(())
    }
    
    /// 检查数据是否存在
    pub async fn exists(&self) -> VaultResult<bool> {
        let config = AdapterConfig::default();
        let factory = StrongholdFactory::default();
        let adapter = factory.create_unified_adapter(Some(config)).await
            .map_err(|e| VaultError::InternalError(format!("创建适配器失败: {}", e)))?;

        let storage_key = format!("{}:{}", self.service_name, self.account_name);
        let exists = adapter.get(&storage_key).await
            .map_err(|e| VaultError::InternalError(format!("检查存在性失败: {}", e)))?
            .is_some();

        Ok(exists)
    }
    
    /// 桌面端密钥存储实现
    #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
    async fn store_key_desktop(&self, key_base64: &str) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service_name, &self.account_name)
            .map_err(|e| VaultError::InternalError(format!("创建密钥链条目失败: {}", e)))?;

        entry
            .set_password(key_base64)
            .map_err(|e| VaultError::InternalError(format!("存储到密钥链失败: {}", e)))?;

        log::debug!("成功存储密钥到桌面密钥链: {}:{}", self.service_name, self.account_name);
        Ok(())
    }
    
    /// 桌面端密钥获取实现
    #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
    async fn get_key_desktop(&self) -> VaultResult<String> {
        use keyring::Entry;

        let entry = Entry::new(&self.service_name, &self.account_name)
            .map_err(|e| VaultError::InternalError(format!("创建密钥链条目失败: {}", e)))?;

        entry.get_password().map_err(|e| match e {
            keyring::Error::NoEntry => VaultError::InternalError("密钥未找到".to_string()),
            _ => VaultError::InternalError(format!("从密钥链获取失败: {}", e)),
        })
    }
    
    /// 桌面端密钥删除实现
    #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
    async fn delete_key_desktop(&self) -> VaultResult<()> {
        use keyring::Entry;

        let entry = Entry::new(&self.service_name, &self.account_name)
            .map_err(|e| VaultError::InternalError(format!("创建密钥链条目失败: {}", e)))?;

        entry
            .delete_password()
            .map_err(|e| VaultError::InternalError(format!("从密钥链删除失败: {}", e)))?;

        log::debug!("成功从桌面密钥链删除密钥: {}:{}", self.service_name, self.account_name);
        Ok(())
    }
    
    /// 移动端密钥存储实现
    #[cfg(any(target_os = "android", target_os = "ios"))]
    async fn store_key_mobile(&self, key_base64: &str) -> VaultResult<()> {
        use crate::mobile::{KeychainSecureStorage, KeychainSecureStorageFactory};

        let mut storage = KeychainSecureStorageFactory::create_default();
        storage.initialize().await
            .map_err(|e| VaultError::InternalError(format!("初始化移动安全存储失败: {}", e)))?;

        let storage_key = format!("{}:{}", self.service_name, self.account_name);
        storage.store(&storage_key, key_base64).await
            .map_err(|e| VaultError::InternalError(format!("存储到移动安全存储失败: {}", e)))?;

        log::debug!("成功存储密钥到移动安全存储: {}", storage_key);
        Ok(())
    }
    
    /// 移动端密钥获取实现
    #[cfg(any(target_os = "android", target_os = "ios"))]
    async fn get_key_mobile(&self) -> VaultResult<String> {
        use crate::mobile::{KeychainSecureStorage, KeychainSecureStorageFactory};

        let mut storage = KeychainSecureStorageFactory::create_default();
        storage.initialize().await
            .map_err(|e| VaultError::InternalError(format!("初始化移动安全存储失败: {}", e)))?;

        let storage_key = format!("{}:{}", self.service_name, self.account_name);
        match storage.retrieve(&storage_key).await {
            Ok(Some(value)) => Ok(value),
            Ok(None) => Err(VaultError::InternalError("密钥未找到".to_string())),
            Err(e) => Err(VaultError::InternalError(format!("从移动安全存储获取失败: {}", e))),
        }
    }
    
    /// 移动端密钥删除实现
    #[cfg(any(target_os = "android", target_os = "ios"))]
    async fn delete_key_mobile(&self) -> VaultResult<()> {
        use crate::mobile::{KeychainSecureStorage, KeychainSecureStorageFactory};

        let mut storage = KeychainSecureStorageFactory::create_default();
        storage.initialize().await
            .map_err(|e| VaultError::InternalError(format!("初始化移动安全存储失败: {}", e)))?;

        let storage_key = format!("{}:{}", self.service_name, self.account_name);
        storage.remove(&storage_key).await
            .map_err(|e| VaultError::InternalError(format!("从移动安全存储删除失败: {}", e)))?;

        log::debug!("成功从移动安全存储删除密钥: {}", storage_key);
        Ok(())
    }
    
    /// 解码 base64 密钥
    fn decode_key(&self, key_base64: &str) -> VaultResult<[u8; KEY_SIZE]> {
        let key_bytes = BASE64_STANDARD
            .decode(key_base64)
            .map_err(|e| VaultError::Base64Decode(e))?;
        
        if key_bytes.len() != KEY_SIZE {
            return Err(VaultError::InternalError(format!(
                "无效的密钥长度: 期望{}字节，实际{}字节",
                KEY_SIZE, key_bytes.len()
            )));
        }
        
        let mut key = [0u8; KEY_SIZE];
        key.copy_from_slice(&key_bytes);
        Ok(key)
    }
}

/// 跨平台密钥链工厂
pub struct CrossPlatformKeychainFactory;

impl CrossPlatformKeychainFactory {
    /// 创建用户主密钥的密钥链管理器
    pub fn create_user_master_key_keychain(contact: &str) -> CrossPlatformKeychain {
        CrossPlatformKeychain::new("secure-password", &format!("{}-master", contact))
    }
    
    /// 创建对称密钥的密钥链管理器
    pub fn create_symmetric_key_keychain(contact: &str) -> CrossPlatformKeychain {
        CrossPlatformKeychain::new("secure-password", &format!("{}-symmetric", contact))
    }
    
    /// 创建私钥的密钥链管理器
    pub fn create_private_key_keychain(contact: &str) -> CrossPlatformKeychain {
        CrossPlatformKeychain::new("secure-password", &format!("{}-private", contact))
    }
    
    /// 检查当前平台是否支持密钥链
    pub fn is_keychain_supported() -> bool {
        #[cfg(any(target_os = "windows", target_os = "macos", target_os = "linux"))]
        {
            true
        }
        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            true
        }
        #[cfg(not(any(
            target_os = "windows",
            target_os = "macos", 
            target_os = "linux",
            target_os = "android",
            target_os = "ios"
        )))]
        {
            false
        }
    }
    
    /// 获取平台特定的密钥链信息
    pub fn get_platform_info() -> PlatformKeychainInfo {
        #[cfg(target_os = "windows")]
        {
            PlatformKeychainInfo {
                platform: "Windows".to_string(),
                keychain_type: "Windows Credential Manager".to_string(),
                supports_biometric: false,
                supports_hardware_security: true,
            }
        }
        
        #[cfg(target_os = "macos")]
        {
            PlatformKeychainInfo {
                platform: "macOS".to_string(),
                keychain_type: "macOS Keychain".to_string(),
                supports_biometric: true, // Touch ID / Face ID
                supports_hardware_security: true, // Secure Enclave
            }
        }
        
        #[cfg(target_os = "linux")]
        {
            PlatformKeychainInfo {
                platform: "Linux".to_string(),
                keychain_type: "Secret Service".to_string(),
                supports_biometric: false,
                supports_hardware_security: false,
            }
        }
        
        #[cfg(target_os = "ios")]
        {
            PlatformKeychainInfo {
                platform: "iOS".to_string(),
                keychain_type: "iOS Keychain".to_string(),
                supports_biometric: true, // Touch ID / Face ID
                supports_hardware_security: true, // Secure Enclave
            }
        }
        
        #[cfg(target_os = "android")]
        {
            PlatformKeychainInfo {
                platform: "Android".to_string(),
                keychain_type: "Android Keystore".to_string(),
                supports_biometric: true, // Fingerprint / Face unlock
                supports_hardware_security: true, // Hardware Security Module
            }
        }
        
        #[cfg(not(any(
            target_os = "windows",
            target_os = "macos",
            target_os = "linux",
            target_os = "android",
            target_os = "ios"
        )))]
        {
            PlatformKeychainInfo {
                platform: "Unknown".to_string(),
                keychain_type: "None".to_string(),
                supports_biometric: false,
                supports_hardware_security: false,
            }
        }
    }
}

/// 平台密钥链信息
#[derive(Debug, Clone)]
pub struct PlatformKeychainInfo {
    pub platform: String,
    pub keychain_type: String,
    pub supports_biometric: bool,
    pub supports_hardware_security: bool,
}

/// 批量密钥管理器
pub struct BatchKeychainManager {
    base_service: String,
}

impl BatchKeychainManager {
    /// 创建新的批量密钥管理器
    pub fn new(base_service: &str) -> Self {
        Self {
            base_service: base_service.to_string(),
        }
    }
    
    /// 批量存储密钥
    pub async fn store_keys(&self, keys: &[(&str, &[u8; KEY_SIZE])]) -> VaultResult<()> {
        for (account, key) in keys {
            let keychain = CrossPlatformKeychain::new(&self.base_service, account);
            keychain.store_key(key).await?;
        }
        Ok(())
    }
    
    /// 批量获取密钥
    pub async fn get_keys(&self, accounts: &[&str]) -> VaultResult<Vec<(String, [u8; KEY_SIZE])>> {
        let mut results = Vec::new();
        
        for account in accounts {
            let keychain = CrossPlatformKeychain::new(&self.base_service, account);
            match keychain.get_key().await {
                Ok(key) => results.push((account.to_string(), key)),
                Err(e) => {
                    warn!("无法获取账户 {} 的密钥: {}", account, e);
                    // 继续处理其他密钥，不中断整个操作
                }
            }
        }
        
        Ok(results)
    }
    
    /// 批量删除密钥
    pub async fn delete_keys(&self, accounts: &[&str]) -> VaultResult<()> {
        for account in accounts {
            let keychain = CrossPlatformKeychain::new(&self.base_service, account);
            if let Err(e) = keychain.delete().await {
                warn!("无法删除账户 {} 的密钥: {}", account, e);
                // 继续处理其他密钥
            }
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[tokio::test]
    async fn test_cross_platform_keychain() {
        let keychain = CrossPlatformKeychain::new("test-service", "test-account");
        
        // 测试密钥存储和获取
        let test_key = [42u8; KEY_SIZE];
        
        // 在测试环境中，这些操作的行为取决于平台
        // 在 CI 环境中可能会失败，所以我们只测试接口
        assert!(!keychain.exists().await.unwrap());
    }
    
    #[test]
    fn test_platform_info() {
        let info = CrossPlatformKeychainFactory::get_platform_info();
        assert!(!info.platform.is_empty());
        assert!(!info.keychain_type.is_empty());
    }
    
    #[test]
    fn test_keychain_support() {
        // 应该在所有支持的平台上返回 true
        assert!(CrossPlatformKeychainFactory::is_keychain_supported());
    }
    
    #[tokio::test]
    async fn test_batch_keychain_manager() {
        let batch_manager = BatchKeychainManager::new("test-batch-service");
        
        let test_keys = vec![
            ("account1", &[1u8; KEY_SIZE]),
            ("account2", &[2u8; KEY_SIZE]),
        ];
        
        // 测试批量操作接口
        // 在测试环境中可能会失败，所以我们只测试接口存在
        let accounts = vec!["account1", "account2"];
        let _ = batch_manager.get_keys(&accounts).await;
    }
} 