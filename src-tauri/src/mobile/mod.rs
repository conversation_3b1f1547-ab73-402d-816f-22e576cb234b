/// 移动端功能模块
/// 
/// 提供跨平台的移动端功能支持，包括安全存储、生物识别、设备信息等

pub mod android;
pub mod biometric;
pub mod commands;
pub mod device_info;
pub mod errors;
pub mod feature_manager;
pub mod ios;
pub mod keychain;
pub mod platform;
pub mod traits;

// 重新导出主要类型
pub use errors::{MobileError, MobileResult};
pub use feature_manager::MobileFeatureManager;
pub use keychain::{
    KeychainConfig, 
    KeychainSecureStorage, 
    KeychainSecureStorageFactory
};
pub use traits::{SecureStorageProvider, PlatformProvider};
pub use platform::{PlatformInfo, MobilePlatform, PlatformDetector};
pub use device_info::{DeviceInfo, MobileDeviceInfo, DeviceInfoCollector, DeviceInfoFactory};
pub use biometric::{BiometricManager, BiometricConfig, BiometricFactory};

// 暂时禁用测试，因为有一些API不匹配的问题
// #[cfg(test)]
// pub mod tests;

// 条件编译的平台特定导出
#[cfg(target_os = "android")]
pub use android::*;

#[cfg(target_os = "ios")]
// pub use ios::*; // 暂时注释掉未使用的导入

/// 移动平台模块版本信息
pub const MOBILE_MODULE_VERSION: &str = "0.1.0";

/// 移动平台模块名称
pub const MOBILE_MODULE_NAME: &str = "secure-password-mobile";

/// 初始化移动平台模块
/// 
/// 这个函数应该在应用启动时调用，用于初始化移动平台的各种功能。
/// 它会自动检测当前平台并配置相应的功能提供者。
/// 
/// # 返回值
/// 返回初始化结果
/// 
/// # 示例
/// ```rust
/// use crate::mobile;
/// 
/// #[tokio::main]
/// async fn main() {
///     match mobile::initialize_mobile_module().await {
///         Ok(_) => println!("移动平台模块初始化成功"),
///         Err(e) => eprintln!("移动平台模块初始化失败: {}", e),
///     }
/// }
/// ```
pub async fn initialize_mobile_module() -> MobileResult<()> {
    log::info!("初始化移动平台模块 v{}", MOBILE_MODULE_VERSION);
    
    // 检测当前平台
    let platform = PlatformDetector::detect_current_platform();
    log::info!("检测到平台: {}", platform);
    
    // 根据平台进行特定初始化
    match platform {
        MobilePlatform::iOS => {
            log::info!("初始化iOS平台功能");
            // iOS特定的初始化逻辑
        }
        MobilePlatform::Android => {
            log::info!("初始化Android平台功能");
            // Android特定的初始化逻辑
        }
        MobilePlatform::Unknown => {
            log::info!("未知平台，使用基础功能");
            // 基础功能初始化
        }
    }
    
    log::info!("移动平台模块初始化完成");
    Ok(())
}

/// 获取移动平台模块信息
/// 
/// # 返回值
/// 返回包含模块信息的HashMap
pub fn get_module_info() -> std::collections::HashMap<String, String> {
    let mut info = std::collections::HashMap::new();
    info.insert("name".to_string(), MOBILE_MODULE_NAME.to_string());
    info.insert("version".to_string(), MOBILE_MODULE_VERSION.to_string());
    info.insert("platform".to_string(), PlatformDetector::detect_current_platform().to_string());
    info.insert("features".to_string(), "secure_storage,biometric".to_string());
    info
}

/// 创建默认的功能管理器
/// 
/// 根据当前平台自动创建并配置功能管理器
/// 
/// # 返回值
/// 返回配置好的功能管理器
pub async fn create_default_feature_manager() -> MobileResult<MobileFeatureManager> {
    let platform = PlatformDetector::detect_current_platform();
    
    match platform {
        MobilePlatform::iOS => {
            let mut manager = MobileFeatureManager::new();
            manager.initialize().await?;
            Ok(manager)
        }
        MobilePlatform::Android => {
            let mut manager = MobileFeatureManager::new();
            manager.initialize().await?;
            Ok(manager)
        }
        MobilePlatform::Unknown => {
            // 对于未知平台，创建一个基本的管理器
            let mut manager = MobileFeatureManager::new();
            manager.initialize().await?;
            Ok(manager)
        }
    }
}

/// 检查平台功能支持
/// 
/// # 返回值
/// 返回平台支持的功能列表
pub fn check_platform_support() -> Vec<String> {
    let platform = PlatformDetector::detect_current_platform();
    let mut features = Vec::new();
    
    if platform.supports_secure_storage() {
        features.push("secure_storage".to_string());
    }
    
    if BiometricFactory::is_platform_supported() {
        features.push("biometric".to_string());
    }
    
    features
}

 