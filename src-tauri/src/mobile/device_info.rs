/// 移动端设备信息管理
/// 
/// 使用 tauri-plugin-machine-uid 和 tauri-plugin-os 提供准确的设备信息

use serde::{Deserialize, Serialize};
use crate::mobile::platform::MobilePlatform;
use crate::mobile::errors::MobileResult;

// 导入 Tauri 插件
#[cfg(any(target_os = "android", target_os = "ios"))]
use tauri_plugin_machine_uid::MachineUidExt;

// 桌面平台的 machine-uid 库
#[cfg(not(any(target_os = "android", target_os = "ios")))]
use machine_uid;

/// 设备信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DeviceInfo {
    /// 平台类型
    pub platform: MobilePlatform,
    /// 平台版本
    pub version: String,
    /// 设备型号
    pub model: String,
    /// 设备制造商
    pub manufacturer: String,
    /// 设备唯一标识符（基于硬件）
    pub device_id: String,
    /// 系统架构
    pub arch: String,
    /// 系统族
    pub family: String,
    /// 操作系统类型
    pub os_type: String,
}

impl Default for DeviceInfo {
    fn default() -> Self {
        let platform = MobilePlatform::current().unwrap_or(MobilePlatform::Unknown);
        Self {
            platform,
            version: "Unknown".to_string(),
            model: "Unknown".to_string(),
            manufacturer: "Unknown".to_string(),
            device_id: "unknown".to_string(),
            arch: std::env::consts::ARCH.to_string(),
            family: std::env::consts::FAMILY.to_string(),
            os_type: std::env::consts::OS.to_string(),
        }
    }
}

/// 增强的移动端设备信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MobileDeviceInfo {
    /// 基础设备信息
    pub basic_info: DeviceInfo,
    /// 是否支持生物识别
    pub supports_biometric: bool,
    /// 是否支持安全存储
    pub supports_secure_storage: bool,
    /// 应用版本
    pub app_version: String,
    /// 设备型号（详细）
    pub device_model: Option<String>,
    /// 系统版本（详细）
    pub system_version: Option<String>,
    /// 设备名称
    pub device_name: String,
    /// 可用存储空间（字节）
    pub available_storage: Option<u64>,
    /// 总存储空间（字节）
    pub total_storage: Option<u64>,
    /// 内存大小（字节）
    pub memory_size: Option<u64>,
    /// 屏幕分辨率
    pub screen_resolution: Option<(u32, u32)>,
    /// 屏幕密度
    pub screen_density: Option<f32>,
    /// 是否支持硬件加密
    pub supports_hardware_encryption: bool,
    /// 网络连接类型
    pub network_type: Option<String>,
    /// 电池电量百分比
    pub battery_level: Option<f32>,
    /// 是否正在充电
    pub is_charging: Option<bool>,
    /// 设备信息收集时间戳
    pub collected_at: chrono::DateTime<chrono::Utc>,
}

impl Default for MobileDeviceInfo {
    fn default() -> Self {
        let platform = MobilePlatform::current().unwrap_or(MobilePlatform::Unknown);
        Self {
            basic_info: DeviceInfo::default(),
            supports_biometric: Self::detect_biometric_support(&platform),
            supports_secure_storage: Self::detect_secure_storage_support(&platform),
            supports_hardware_encryption: Self::detect_hardware_encryption_support(&platform),
            app_version: env!("CARGO_PKG_VERSION").to_string(),
            device_model: None,
            system_version: None,
            device_name: "Unknown Device".to_string(),
            available_storage: None,
            total_storage: None,
            memory_size: None,
            screen_resolution: None,
            screen_density: None,
            network_type: None,
            battery_level: None,
            is_charging: None,
            collected_at: chrono::Utc::now(),
        }
    }
}

impl MobileDeviceInfo {
    /// 创建新的设备信息实例
    /// 
    /// # 参数
    /// * `platform` - 平台类型
    /// 
    /// # 返回值
    /// 返回设备信息实例
    pub fn new(platform: MobilePlatform) -> Self {
        let mut basic_info = DeviceInfo::default();
        basic_info.platform = platform.clone();
        
        Self {
            basic_info,
            supports_biometric: Self::detect_biometric_support(&platform),
            supports_secure_storage: Self::detect_secure_storage_support(&platform),
            supports_hardware_encryption: Self::detect_hardware_encryption_support(&platform),
            app_version: env!("CARGO_PKG_VERSION").to_string(),
            device_name: format!("{} Device", platform.name()),
            collected_at: chrono::Utc::now(),
            ..Default::default()
        }
    }

    /// 检测生物识别支持
    /// 
    /// # 参数
    /// * `platform` - 平台类型
    /// 
    /// # 返回值
    /// 支持返回true，不支持返回false
    fn detect_biometric_support(platform: &MobilePlatform) -> bool {
        match platform {
            MobilePlatform::iOS => {
                // iOS 支持 Touch ID 和 Face ID
                true
            }
            MobilePlatform::Android => {
                // Android 支持指纹识别和面部识别
                true
            }
            MobilePlatform::Unknown => false,
        }
    }

    /// 检测安全存储支持
    /// 
    /// # 参数
    /// * `platform` - 平台类型
    /// 
    /// # 返回值
    /// 支持返回true，不支持返回false
    fn detect_secure_storage_support(platform: &MobilePlatform) -> bool {
        match platform {
            MobilePlatform::iOS => true, // iOS Keychain
            MobilePlatform::Android => true, // Android Keystore
            MobilePlatform::Unknown => false,
        }
    }

    /// 检测硬件加密支持
    /// 
    /// # 参数
    /// * `platform` - 平台类型
    /// 
    /// # 返回值
    /// 支持返回true，不支持返回false
    fn detect_hardware_encryption_support(platform: &MobilePlatform) -> bool {
        match platform {
            MobilePlatform::iOS => {
                // iOS 支持 Secure Enclave
                true
            }
            MobilePlatform::Android => {
                // Android 支持 Hardware Security Module
                true
            }
            MobilePlatform::Unknown => false,
        }
    }

    /// 设置设备型号
    /// 
    /// # 参数
    /// * `model` - 设备型号
    pub fn set_device_model(&mut self, model: String) {
        self.device_model = Some(model.clone());
        self.basic_info.model = model;
    }

    /// 设置系统版本
    /// 
    /// # 参数
    /// * `version` - 系统版本
    pub fn set_system_version(&mut self, version: String) {
        self.system_version = Some(version.clone());
        self.basic_info.version = version;
    }

    /// 设置设备ID
    /// 
    /// # 参数
    /// * `id` - 设备ID
    pub fn set_device_id(&mut self, id: String) {
        self.basic_info.device_id = id;
    }

    /// 设置设备名称
    /// 
    /// # 参数
    /// * `name` - 设备名称
    pub fn set_device_name(&mut self, name: String) {
        self.device_name = name;
    }

    /// 设置存储信息
    /// 
    /// # 参数
    /// * `available` - 可用存储空间（字节）
    /// * `total` - 总存储空间（字节）
    pub fn set_storage_info(&mut self, available: u64, total: u64) {
        self.available_storage = Some(available);
        self.total_storage = Some(total);
    }

    /// 设置内存大小
    /// 
    /// # 参数
    /// * `size` - 内存大小（字节）
    pub fn set_memory_size(&mut self, size: u64) {
        self.memory_size = Some(size);
    }

    /// 设置屏幕信息
    /// 
    /// # 参数
    /// * `width` - 屏幕宽度
    /// * `height` - 屏幕高度
    /// * `density` - 屏幕密度
    pub fn set_screen_info(&mut self, width: u32, height: u32, density: f32) {
        self.screen_resolution = Some((width, height));
        self.screen_density = Some(density);
    }

    /// 设置网络类型
    /// 
    /// # 参数
    /// * `network_type` - 网络类型
    pub fn set_network_type(&mut self, network_type: String) {
        self.network_type = Some(network_type);
    }

    /// 设置电池信息
    /// 
    /// # 参数
    /// * `level` - 电池电量百分比
    /// * `is_charging` - 是否正在充电
    pub fn set_battery_info(&mut self, level: f32, is_charging: bool) {
        self.battery_level = Some(level);
        self.is_charging = Some(is_charging);
    }

    /// 计算存储使用率
    /// 
    /// # 返回值
    /// 返回存储使用率（0.0-1.0），如果信息不可用则返回None
    pub fn storage_usage_ratio(&self) -> Option<f32> {
        if let (Some(available), Some(total)) = (self.available_storage, self.total_storage) {
            if total > 0 {
                let used = total.saturating_sub(available);
                Some(used as f32 / total as f32)
            } else {
                None
            }
        } else {
            None
        }
    }

    /// 检查是否有足够的存储空间
    /// 
    /// # 参数
    /// * `required_bytes` - 需要的字节数
    /// 
    /// # 返回值
    /// 有足够空间返回true，否则返回false
    pub fn has_sufficient_storage(&self, required_bytes: u64) -> bool {
        self.available_storage
            .map(|available| available >= required_bytes)
            .unwrap_or(false)
    }

    /// 获取设备能力摘要
    /// 
    /// # 返回值
    /// 返回设备能力的字符串描述
    pub fn capabilities_summary(&self) -> String {
        let mut capabilities = Vec::new();
        
        if self.supports_biometric {
            capabilities.push("生物识别");
        }
        if self.supports_secure_storage {
            capabilities.push("安全存储");
        }
        if self.supports_hardware_encryption {
            capabilities.push("硬件加密");
        }
        
        if capabilities.is_empty() {
            "无特殊能力".to_string()
        } else {
            capabilities.join(", ")
        }
    }

    /// 序列化为JSON
    /// 
    /// # 返回值
    /// 返回JSON字符串或错误信息
    pub fn to_json(&self) -> Result<String, String> {
        serde_json::to_string_pretty(self)
            .map_err(|e| format!("序列化失败: {}", e))
    }

    /// 从JSON反序列化
    /// 
    /// # 参数
    /// * `json` - JSON字符串
    /// 
    /// # 返回值
    /// 返回设备信息实例或错误信息
    pub fn from_json(json: &str) -> Result<Self, String> {
        serde_json::from_str(json)
            .map_err(|e| format!("反序列化失败: {}", e))
    }

    /// 刷新时间戳
    pub fn refresh_timestamp(&mut self) {
        self.collected_at = chrono::Utc::now();
    }
}

/// 设备信息收集器
pub struct DeviceInfoCollector;

impl DeviceInfoCollector {
    /// 使用 Tauri AppHandle 收集设备信息（移动平台）
    /// 
    /// # 参数
    /// * `app_handle` - Tauri 应用句柄
    /// 
    /// # 返回值
    /// 返回收集到的设备信息
    #[cfg(any(target_os = "android", target_os = "ios"))]
    pub async fn collect_with_app_handle(app_handle: &tauri::AppHandle) -> MobileDeviceInfo {
        let platform = MobilePlatform::current().unwrap_or(MobilePlatform::Unknown);
        let mut info = MobileDeviceInfo::new(platform);

        // 使用 tauri-plugin-machine-uid 获取设备唯一标识
        match app_handle.machine_uid().get_machine_uid() {
            Ok(machine_uid_result) => {
                if let Some(id) = machine_uid_result.id {
                    log::info!("成功获取机器唯一标识: {}", id);
                    info.set_device_id(id);
                } else {
                    log::warn!("机器唯一标识为空，使用备用方案");
                    let fallback_id = uuid::Uuid::new_v4().to_string();
                    info.set_device_id(fallback_id.clone());
                    log::info!("移动平台使用备用设备ID: {}", fallback_id);
                }
            }
            Err(e) => {
                log::warn!("获取机器唯一标识失败: {}, 使用备用方案", e);
                let fallback_id = uuid::Uuid::new_v4().to_string();
                info.set_device_id(fallback_id.clone());
                log::info!("移动平台使用备用设备ID: {}", fallback_id);
            }
        }

        // 收集操作系统信息
        Self::collect_os_info(&mut info).await;

        // 根据平台收集特定信息
        match info.basic_info.platform {
            MobilePlatform::iOS => {
                Self::collect_ios_info(&mut info).await;
            }
            MobilePlatform::Android => {
                Self::collect_android_info(&mut info).await;
            }
            MobilePlatform::Unknown => {
                log::warn!("未知平台，跳过平台特定信息收集");
            }
        }

        info.refresh_timestamp();
        info
    }

    /// 收集设备信息（桌面平台）
    /// 
    /// # 返回值
    /// 返回收集到的设备信息
    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    pub async fn collect_info() -> MobileDeviceInfo {
        let platform = MobilePlatform::current().unwrap_or(MobilePlatform::Unknown);
        let mut info = MobileDeviceInfo::new(platform);

        // 使用 machine-uid 库获取设备唯一标识
        match machine_uid::machine_uid() {
            Ok(machine_uid) => {
                log::info!("成功获取机器唯一标识: {}", machine_uid);
                info.set_device_id(machine_uid);
            }
            Err(e) => {
                log::warn!("获取机器唯一标识失败: {}, 使用备用方案", e);
                let fallback_id = uuid::Uuid::new_v4().to_string();
                info.set_device_id(fallback_id.clone());
                log::info!("桌面平台使用备用设备ID: {}", fallback_id);
            }
        }

        // 收集操作系统信息
        Self::collect_os_info(&mut info).await;

        info.refresh_timestamp();
        info
    }

    /// 收集设备信息的异步方法
    /// 
    /// # 返回值
    /// 返回收集到的设备信息
    pub async fn collect_device_info(&mut self) -> MobileResult<MobileDeviceInfo> {
        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            // 移动平台需要 app_handle，这里返回默认信息
            let info = MobileDeviceInfo::default();
            Ok(info)
        }
        
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            let info = Self::collect_info().await;
            Ok(info)
        }
    }

    /// 收集操作系统信息
    /// 
    /// # 参数
    /// * `info` - 设备信息实例
    async fn collect_os_info(info: &mut MobileDeviceInfo) {
        // 使用编译时信息设置基础系统信息
        info.basic_info.arch = std::env::consts::ARCH.to_string();
        info.basic_info.family = std::env::consts::FAMILY.to_string();
        info.basic_info.os_type = std::env::consts::OS.to_string();

        // 设置平台版本（使用编译时信息作为备用）
        let version = match std::env::consts::OS {
            "windows" => "Windows".to_string(),
            "macos" => "macOS".to_string(),
            "linux" => "Linux".to_string(),
            "ios" => "iOS".to_string(),
            "android" => "Android".to_string(),
            _ => "Unknown".to_string(),
        };
        info.set_system_version(version);

        // 设置制造商信息
        let manufacturer = match std::env::consts::OS {
            "windows" => "Microsoft",
            "macos" | "ios" => "Apple",
            "linux" => "Linux Foundation",
            "android" => "Google",
            _ => "Unknown",
        };
        info.basic_info.manufacturer = manufacturer.to_string();
    }

    /// 收集iOS特定信息
    /// 
    /// # 参数
    /// * `info` - 设备信息实例
    async fn collect_ios_info(info: &mut MobileDeviceInfo) {
        info.set_device_model("iOS Device".to_string());
        info.set_device_name("iOS Device".to_string());
        
        // iOS特定的默认值
        info.supports_biometric = true;
        info.supports_secure_storage = true;
        info.supports_hardware_encryption = true;
        
        log::info!("已收集iOS设备信息");
    }

    /// 收集Android特定信息
    /// 
    /// # 参数
    /// * `info` - 设备信息实例
    async fn collect_android_info(info: &mut MobileDeviceInfo) {
        info.set_device_model("Android Device".to_string());
        info.set_device_name("Android Device".to_string());
        
        // Android特定的默认值
        info.supports_biometric = true;
        info.supports_secure_storage = true;
        info.supports_hardware_encryption = true;
        
        log::info!("已收集Android设备信息");
    }

    /// 获取详细的平台信息
    /// 
    /// # 返回值
    /// 返回包含详细平台信息的HashMap
    pub fn get_detailed_platform_info() -> std::collections::HashMap<String, String> {
        let mut info = std::collections::HashMap::new();
        
        info.insert("arch".to_string(), std::env::consts::ARCH.to_string());
        info.insert("family".to_string(), std::env::consts::FAMILY.to_string());
        info.insert("os".to_string(), std::env::consts::OS.to_string());
        info.insert("dll_extension".to_string(), std::env::consts::DLL_EXTENSION.to_string());
        info.insert("dll_prefix".to_string(), std::env::consts::DLL_PREFIX.to_string());
        info.insert("dll_suffix".to_string(), std::env::consts::DLL_SUFFIX.to_string());
        info.insert("exe_extension".to_string(), std::env::consts::EXE_EXTENSION.to_string());
        info.insert("exe_suffix".to_string(), std::env::consts::EXE_SUFFIX.to_string());
        
        // 添加编译时信息（使用运行时环境变量）
        if let Ok(target_arch) = std::env::var("CARGO_CFG_TARGET_ARCH") {
            info.insert("target_arch".to_string(), target_arch);
        } else {
            info.insert("target_arch".to_string(), std::env::consts::ARCH.to_string());
        }
        
        if let Ok(target_os) = std::env::var("CARGO_CFG_TARGET_OS") {
            info.insert("target_os".to_string(), target_os);
        } else {
            info.insert("target_os".to_string(), std::env::consts::OS.to_string());
        }
        
        if let Ok(target_family) = std::env::var("CARGO_CFG_TARGET_FAMILY") {
            info.insert("target_family".to_string(), target_family);
        } else {
            info.insert("target_family".to_string(), std::env::consts::FAMILY.to_string());
        }
        
        if let Ok(target_vendor) = std::env::var("CARGO_CFG_TARGET_VENDOR") {
            info.insert("target_vendor".to_string(), target_vendor);
        } else {
            info.insert("target_vendor".to_string(), "unknown".to_string());
        }
        
        info
    }
}

/// 设备信息工厂
pub struct DeviceInfoFactory;

impl DeviceInfoFactory {
    /// 创建设备信息收集器
    /// 
    /// # 返回值
    /// 返回设备信息收集器实例
    pub fn create_collector() -> DeviceInfoCollector {
        DeviceInfoCollector
    }

    /// 获取基础设备信息
    /// 
    /// # 返回值
    /// 返回基础设备信息
    pub fn get_basic_info() -> DeviceInfo {
        let platform = MobilePlatform::current().unwrap_or(MobilePlatform::Unknown);
        let mut info = DeviceInfo::default();
        info.platform = platform;
        
        // 设置基础信息
        info.arch = std::env::consts::ARCH.to_string();
        info.family = std::env::consts::FAMILY.to_string();
        info.os_type = std::env::consts::OS.to_string();
        
        info
    }

    /// 检查平台能力
    /// 
    /// # 返回值
    /// 返回平台能力的HashMap
    pub fn check_platform_capabilities() -> std::collections::HashMap<String, bool> {
        let mut capabilities = std::collections::HashMap::new();
        let platform = MobilePlatform::current().unwrap_or(MobilePlatform::Unknown);
        
        capabilities.insert("biometric".to_string(), MobileDeviceInfo::detect_biometric_support(&platform));
        capabilities.insert("secure_storage".to_string(), MobileDeviceInfo::detect_secure_storage_support(&platform));
        capabilities.insert("hardware_encryption".to_string(), MobileDeviceInfo::detect_hardware_encryption_support(&platform));
        capabilities.insert("is_mobile".to_string(), matches!(platform, MobilePlatform::iOS | MobilePlatform::Android));
        capabilities.insert("is_desktop".to_string(), matches!(platform, MobilePlatform::Unknown));
        
        // 添加编译时能力检查
        capabilities.insert("is_unix".to_string(), cfg!(unix));
        capabilities.insert("is_windows".to_string(), cfg!(windows));
        
        capabilities
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_device_info_creation() {
        let info = DeviceInfo::default();
        assert!(!info.device_id.is_empty());
        assert!(!info.arch.is_empty());
        assert!(!info.family.is_empty());
        assert!(!info.os_type.is_empty());
    }

    #[test]
    fn test_device_info_updates() {
        let mut info = MobileDeviceInfo::default();
        
        info.set_device_model("Test Model".to_string());
        assert_eq!(info.device_model, Some("Test Model".to_string()));
        assert_eq!(info.basic_info.model, "Test Model");
        
        info.set_device_id("test-id".to_string());
        assert_eq!(info.basic_info.device_id, "test-id");
        
        info.set_storage_info(1000, 2000);
        assert_eq!(info.available_storage, Some(1000));
        assert_eq!(info.total_storage, Some(2000));
        assert_eq!(info.storage_usage_ratio(), Some(0.5));
    }

    #[test]
    fn test_device_info_serialization() {
        let info = MobileDeviceInfo::default();
        let json = info.to_json().unwrap();
        assert!(!json.is_empty());
        
        let deserialized = MobileDeviceInfo::from_json(&json).unwrap();
        assert_eq!(info.app_version, deserialized.app_version);
    }

    #[test]
    fn test_capabilities_summary() {
        let info = MobileDeviceInfo::new(MobilePlatform::iOS);
        let summary = info.capabilities_summary();
        assert!(summary.contains("生物识别"));
        assert!(summary.contains("安全存储"));
        assert!(summary.contains("硬件加密"));
    }

    #[test]
    fn test_device_info_factory() {
        let collector = DeviceInfoFactory::create_collector();
        let basic_info = DeviceInfoFactory::get_basic_info();
        let capabilities = DeviceInfoFactory::check_platform_capabilities();
        
        assert!(!basic_info.arch.is_empty());
        assert!(!capabilities.is_empty());
    }

    #[test]
    fn test_detailed_platform_info() {
        let info = DeviceInfoCollector::get_detailed_platform_info();
        assert!(info.contains_key("arch"));
        assert!(info.contains_key("family"));
        assert!(info.contains_key("os"));
        assert!(info.contains_key("target_arch"));
    }

    #[tokio::test]
    async fn test_device_info_collector() {
        #[cfg(any(target_os = "android", target_os = "ios"))]
        {
            // 移动平台使用默认信息进行测试
            let info = MobileDeviceInfo::default();
            assert!(!info.basic_info.device_id.is_empty());
            assert!(!info.app_version.is_empty());
        }
        
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            let info = DeviceInfoCollector::collect_info().await;
            assert!(!info.basic_info.device_id.is_empty());
            assert!(!info.app_version.is_empty());
        }
    }

    #[test]
    fn test_timestamp_refresh() {
        let mut info = MobileDeviceInfo::default();
        let original_time = info.collected_at;
        
        std::thread::sleep(std::time::Duration::from_millis(10));
        info.refresh_timestamp();
        
        assert!(info.collected_at > original_time);
    }
} 