/// 移动端功能提供者抽象接口
/// 
/// 定义移动端各种功能的统一接口

use async_trait::async_trait;
use crate::mobile::errors::MobileResult;
use crate::mobile::{device_info::DeviceInfo, platform::PlatformInfo};

/// 安全存储提供者接口
/// 
/// 定义跨平台的安全存储功能接口
#[async_trait]
pub trait SecureStorageProvider: Send + Sync {
    /// 初始化安全存储
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    async fn initialize(&mut self) -> MobileResult<()>;

    /// 存储安全数据
    /// 
    /// # 参数
    /// * `key` - 存储键名
    /// * `value` - 存储值
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    async fn store(&self, key: &str, value: &str) -> MobileResult<()>;

    /// 获取安全数据
    /// 
    /// # 参数
    /// * `key` - 存储键名
    /// 
    /// # 返回值
    /// 成功返回存储的值，不存在返回None，失败返回错误信息
    async fn retrieve(&self, key: &str) -> MobileResult<Option<String>>;

    /// 删除安全数据
    /// 
    /// # 参数
    /// * `key` - 存储键名
    /// 
    /// # 返回值
    /// 成功返回是否删除，失败返回错误信息
    async fn remove(&self, key: &str) -> MobileResult<bool>;

    /// 检查键是否存在
    /// 
    /// # 参数
    /// * `key` - 存储键名
    /// 
    /// # 返回值
    /// 存在返回true，不存在返回false
    async fn exists(&self, key: &str) -> MobileResult<bool>;

    /// 清空所有数据
    /// 
    /// # 返回值
    /// 成功返回Ok(())，失败返回错误信息
    async fn clear(&self) -> MobileResult<()>;

    /// 获取所有键名
    /// 
    /// # 返回值
    /// 返回所有键名的列表
    async fn get_all_keys(&self) -> MobileResult<Vec<String>>;

    /// 检查是否已初始化
    /// 
    /// # 返回值
    /// 已初始化返回true，未初始化返回false
    async fn is_initialized(&self) -> bool;
}

/// 平台提供者接口
/// 
/// 定义平台特定功能的接口
#[async_trait]
pub trait PlatformProvider: Send + Sync {
    /// 获取设备信息
    /// 
    /// # 返回值
    /// 返回设备信息
    async fn get_device_info(&self) -> MobileResult<DeviceInfo>;

    /// 获取平台信息
    /// 
    /// # 返回值
    /// 返回平台信息
    async fn get_platform_info(&self) -> MobileResult<PlatformInfo>;

    /// 检查功能是否可用
    /// 
    /// # 参数
    /// * `feature` - 功能名称
    /// 
    /// # 返回值
    /// 可用返回true，不可用返回false
    async fn is_feature_available(&self, feature: &str) -> MobileResult<bool>;
} 