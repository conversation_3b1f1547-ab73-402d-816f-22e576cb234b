//! 移动平台Tauri命令模块
//! 
//! 提供前端调用的移动平台功能接口

use crate::mobile::{
    feature_manager::MobileFeatureManager,
    device_info::{DeviceInfo, MobileDeviceInfo, DeviceInfoCollector, DeviceInfoFactory},
    platform::PlatformInfo,
    biometric::{BiometricManager, BiometricStatus},
};
use std::sync::Arc;
use tokio::sync::RwLock;
use tauri::{AppHandle, State};

// 导入必要的依赖
#[cfg(any(target_os = "android", target_os = "ios"))]
use tauri_plugin_machine_uid::MachineUidExt;

// 桌面平台的 machine-uid 库
#[cfg(not(any(target_os = "android", target_os = "ios")))]
use machine_uid;

/// 全局移动功能管理器状态
pub type MobileManagerState = Arc<RwLock<Option<MobileFeatureManager>>>;

/// 全局生物识别管理器状态
pub type BiometricManagerState = Arc<RwLock<Option<BiometricManager>>>;

/// 初始化移动功能管理器
/// 
/// # 参数
/// * `app_handle` - Tauri 应用句柄
/// * `manager_state` - 管理器状态
/// 
/// # 返回值
/// 成功返回初始化状态，失败返回错误信息
#[tauri::command]
pub async fn initialize_mobile_feature_manager(
    _app_handle: AppHandle,
    manager_state: State<'_, MobileManagerState>,
) -> Result<String, String> {
    log::info!("开始初始化移动功能管理器");

    let mut manager_guard = manager_state.write().await;
    
    if manager_guard.is_some() {
        return Ok("移动功能管理器已经初始化".to_string());
    }

    let mut manager = MobileFeatureManager::new();
    
    match manager.initialize().await {
        Ok(_) => {
            *manager_guard = Some(manager);
            log::info!("移动功能管理器初始化成功");
            Ok("移动功能管理器初始化成功".to_string())
        }
        Err(e) => {
            log::error!("移动功能管理器初始化失败: {}", e);
            Err(format!("初始化失败: {}", e))
        }
    }
}

/// 获取移动平台状态
/// 
/// # 参数
/// * `manager_state` - 管理器状态
/// 
/// # 返回值
/// 返回平台状态信息
#[tauri::command]
pub async fn get_mobile_platform_status(
    manager_state: State<'_, MobileManagerState>,
) -> Result<String, String> {
    let manager_guard = manager_state.read().await;
    
    if let Some(manager) = manager_guard.as_ref() {
        if manager.is_initialized() {
            Ok("移动平台已初始化并正常运行".to_string())
        } else {
            Ok("移动平台已创建但未初始化".to_string())
        }
    } else {
        Ok("移动平台未初始化".to_string())
    }
}

/// 重新初始化移动平台
/// 
/// # 参数
/// * `app_handle` - Tauri 应用句柄
/// * `manager_state` - 管理器状态
/// 
/// # 返回值
/// 返回重新初始化结果
#[tauri::command]
pub async fn reinitialize_mobile_platform(
    app_handle: AppHandle,
    manager_state: State<'_, MobileManagerState>,
) -> Result<String, String> {
    log::info!("开始重新初始化移动平台");
    
    // 先清除现有的管理器
    {
        let mut manager_guard = manager_state.write().await;
        *manager_guard = None;
    }
    
    // 重新初始化
    initialize_mobile_feature_manager(app_handle, manager_state).await
}

/// 获取基础设备信息
/// 
/// # 返回值
/// 返回基础设备信息
#[tauri::command]
pub async fn get_device_info() -> Result<DeviceInfo, String> {
    log::info!("获取基础设备信息");
    Ok(DeviceInfoFactory::get_basic_info())
}

/// 获取详细的移动设备信息
/// 
/// # 参数
/// * `app_handle` - Tauri 应用句柄
/// 
/// # 返回值
/// 返回详细的移动设备信息
#[tauri::command]
pub async fn get_mobile_device_info(
    app_handle: AppHandle,
) -> Result<MobileDeviceInfo, String> {
    log::info!("获取详细的移动设备信息");
    
    #[cfg(any(target_os = "android", target_os = "ios"))]
    {
        let device_info = DeviceInfoCollector::collect_with_app_handle(&app_handle).await;
        Ok(device_info)
    }
    
    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    {
        let device_info = DeviceInfoCollector::collect_info().await;
        Ok(device_info)
    }
}

/// 获取设备唯一标识符
/// 
/// # 参数
/// * `app_handle` - Tauri 应用句柄
/// 
/// # 返回值
/// 返回设备唯一标识符
#[tauri::command]
pub async fn get_device_uid(
    app_handle: AppHandle,
) -> Result<String, String> {
    log::info!("获取设备唯一标识符");
    
    #[cfg(any(target_os = "android", target_os = "ios"))]
    {
        match app_handle.machine_uid().get_machine_uid() {
            Ok(machine_uid_result) => {
                if let Some(id) = machine_uid_result.id {
                    log::info!("成功获取机器唯一标识: {}", id);
                    Ok(id)
                } else {
                    log::warn!("机器唯一标识为空，使用备用方案");
                    let fallback_id = uuid::Uuid::new_v4().to_string();
                    Ok(fallback_id)
                }
            }
            Err(e) => {
                log::warn!("获取机器唯一标识失败: {}, 使用备用方案", e);
                let fallback_id = uuid::Uuid::new_v4().to_string();
                Ok(fallback_id)
            }
        }
    }
    
    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    {
        match machine_uid::machine_uid() {
            Ok(machine_uid) => {
                log::info!("成功获取机器唯一标识: {}", machine_uid);
                Ok(machine_uid)
            }
            Err(e) => {
                log::warn!("获取机器唯一标识失败: {}, 使用备用方案", e);
                let fallback_id = uuid::Uuid::new_v4().to_string();
                Ok(fallback_id)
            }
        }
    }
}

/// 获取平台信息
/// 
/// # 返回值
/// 返回平台信息
#[tauri::command]
pub async fn get_platform_info() -> Result<PlatformInfo, String> {
    log::info!("获取平台信息");
    Ok(PlatformInfo::current())
}

/// 获取详细的平台信息
/// 
/// # 返回值
/// 返回详细的平台信息
#[tauri::command]
pub async fn get_detailed_platform_info() -> Result<std::collections::HashMap<String, String>, String> {
    log::info!("获取详细的平台信息");
    Ok(DeviceInfoCollector::get_detailed_platform_info())
}

/// 检查平台能力
/// 
/// # 返回值
/// 返回平台能力信息
#[tauri::command]
pub async fn check_platform_capabilities() -> Result<std::collections::HashMap<String, bool>, String> {
    log::info!("检查平台能力");
    Ok(DeviceInfoFactory::check_platform_capabilities())
}

/// 保存安全数据（重命名自 store_secure_data）
/// 
/// # 参数
/// * `key` - 存储键名
/// * `value` - 存储值
/// * `manager_state` - 管理器状态
/// 
/// # 返回值
/// 成功返回Ok(())，失败返回错误信息
#[tauri::command]
pub async fn save_secure_data(
    key: String,
    value: String,
    manager_state: State<'_, MobileManagerState>,
) -> Result<(), String> {
    let manager_guard = manager_state.read().await;
    
    if let Some(manager) = manager_guard.as_ref() {
        if let Some(storage) = &manager.secure_storage {
            let storage_guard = storage.read().await;
            storage_guard.store(&key, &value).await
                .map_err(|e| format!("存储安全数据失败: {}", e))
        } else {
            Err("安全存储未初始化".to_string())
        }
    } else {
        Err("移动功能管理器未初始化".to_string())
    }
}

/// 获取安全数据（重命名自 retrieve_secure_data）
/// 
/// # 参数
/// * `key` - 存储键名
/// * `manager_state` - 管理器状态
/// 
/// # 返回值
/// 成功返回存储的值，不存在返回None，失败返回错误信息
#[tauri::command]
pub async fn get_secure_data(
    key: String,
    manager_state: State<'_, MobileManagerState>,
) -> Result<Option<String>, String> {
    let manager_guard = manager_state.read().await;
    
    if let Some(manager) = manager_guard.as_ref() {
        if let Some(storage) = &manager.secure_storage {
            let storage_guard = storage.read().await;
            storage_guard.retrieve(&key).await
                .map_err(|e| format!("获取安全数据失败: {}", e))
        } else {
            Err("安全存储未初始化".to_string())
        }
    } else {
        Err("移动功能管理器未初始化".to_string())
    }
}

/// 删除安全数据
/// 
/// # 参数
/// * `key` - 存储键名
/// * `manager_state` - 管理器状态
/// 
/// # 返回值
/// 成功返回是否删除，失败返回错误信息
#[tauri::command]
pub async fn remove_secure_data(
    key: String,
    manager_state: State<'_, MobileManagerState>,
) -> Result<bool, String> {
    let manager_guard = manager_state.read().await;
    
    if let Some(manager) = manager_guard.as_ref() {
        if let Some(storage) = &manager.secure_storage {
            let storage_guard = storage.read().await;
            storage_guard.remove(&key).await
                .map_err(|e| format!("删除安全数据失败: {}", e))
        } else {
            Err("安全存储未初始化".to_string())
        }
    } else {
        Err("移动功能管理器未初始化".to_string())
    }
}

/// 清空安全存储
/// 
/// # 参数
/// * `manager_state` - 管理器状态
/// 
/// # 返回值
/// 成功返回Ok(())，失败返回错误信息
#[tauri::command]
pub async fn clear_secure_storage(
    manager_state: State<'_, MobileManagerState>,
) -> Result<(), String> {
    let manager_guard = manager_state.read().await;
    
    if let Some(manager) = manager_guard.as_ref() {
        if let Some(storage) = &manager.secure_storage {
            let storage_guard = storage.read().await;
            storage_guard.clear().await
                .map_err(|e| format!("清空安全存储失败: {}", e))
        } else {
            Err("安全存储未初始化".to_string())
        }
    } else {
        Err("移动功能管理器未初始化".to_string())
    }
}

/// 获取安全存储键名列表
/// 
/// # 参数
/// * `manager_state` - 管理器状态
/// 
/// # 返回值
/// 返回所有键名的列表
#[tauri::command]
pub async fn get_secure_storage_keys(
    manager_state: State<'_, MobileManagerState>,
) -> Result<Vec<String>, String> {
    let manager_guard = manager_state.read().await;
    
    if let Some(manager) = manager_guard.as_ref() {
        if let Some(storage) = &manager.secure_storage {
            let storage_guard = storage.read().await;
            storage_guard.get_all_keys().await
                .map_err(|e| format!("获取安全存储键名失败: {}", e))
        } else {
            Err("安全存储未初始化".to_string())
        }
    } else {
        Err("移动功能管理器未初始化".to_string())
    }
}

/// 检查生物识别可用性
/// 
/// # 参数
/// * `app_handle` - Tauri 应用句柄
/// 
/// # 返回值
/// 返回生物识别状态信息
#[tauri::command]
pub async fn check_biometric_availability(
    app_handle: AppHandle,
) -> Result<BiometricStatus, String> {
    log::info!("检查生物识别可用性");

    #[cfg(any(target_os = "android", target_os = "ios"))]
    {
        let biometric_manager = BiometricManager::with_default_config(app_handle);
        match biometric_manager.check_status().await {
            Ok(status) => {
                log::info!("生物识别状态检查成功: {:?}", status);
                Ok(status)
            }
            Err(e) => {
                log::error!("生物识别状态检查失败: {}", e);
                Err(format!("检查生物识别可用性失败: {}", e))
            }
        }
    }

    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    {
        log::warn!("当前平台不支持生物识别");
        Ok(BiometricStatus {
            is_available: false,
            error: Some("当前平台不支持生物识别".to_string()),
            biometric_types: vec![],
        })
    }
}

/// 使用生物识别进行认证
/// 
/// # 参数
/// * `reason` - 认证原因
/// * `app_handle` - Tauri 应用句柄
/// 
/// # 返回值
/// 认证成功返回true，失败返回false
#[tauri::command]
pub async fn authenticate_with_biometric(
    reason: String,
    app_handle: AppHandle,
) -> Result<bool, String> {
    log::info!("开始生物识别认证，原因: {}", reason);

    #[cfg(any(target_os = "android", target_os = "ios"))]
    {
        let biometric_manager = BiometricManager::with_default_config(app_handle);
        
        // 首先检查生物识别是否可用
        match biometric_manager.is_available().await {
            Ok(true) => {
                // 生物识别可用，进行认证
                match biometric_manager.authenticate(&reason).await {
                    Ok(result) => {
                        if result {
                            log::info!("生物识别认证成功");
                        } else {
                            log::warn!("生物识别认证失败或被用户取消");
                        }
                        Ok(result)
                    }
                    Err(e) => {
                        log::error!("生物识别认证过程中出错: {}", e);
                        Err(format!("生物识别认证失败: {}", e))
                    }
                }
            }
            Ok(false) => {
                log::warn!("生物识别不可用");
                Err("生物识别不可用".to_string())
            }
            Err(e) => {
                log::error!("检查生物识别可用性失败: {}", e);
                Err(format!("检查生物识别可用性失败: {}", e))
            }
        }
    }

    #[cfg(not(any(target_os = "android", target_os = "ios")))]
    {
        log::warn!("当前平台不支持生物识别认证");
        Err("当前平台不支持生物识别认证".to_string())
    }
}

/// 获取移动功能状态
/// 
/// # 参数
/// * `manager_state` - 管理器状态
/// 
/// # 返回值
/// 成功返回状态摘要，失败返回错误信息
#[tauri::command]
pub async fn get_mobile_status(
    manager_state: State<'_, MobileManagerState>,
) -> Result<String, String> {
    log::info!("获取移动功能状态");

    let manager_guard = manager_state.read().await;
    
    if let Some(manager) = manager_guard.as_ref() {
        match manager.get_status_summary().await {
            Ok(status) => {
                log::info!("移动功能状态获取成功");
                Ok(status)
            }
            Err(e) => {
                log::error!("获取移动功能状态失败: {}", e);
                Err(format!("获取状态失败: {}", e))
            }
        }
    } else {
        Ok("移动功能管理器未初始化".to_string())
    }
}

/// 获取所有移动端命令
/// 
/// # 返回值
/// 返回命令名称列表
pub fn get_mobile_commands() -> Vec<String> {
    vec![
        "initialize_mobile_feature_manager".to_string(),
        "get_device_info".to_string(),
        "get_mobile_device_info".to_string(),
        "get_device_uid".to_string(),
        "get_platform_info".to_string(),
        "get_detailed_platform_info".to_string(),
        "check_platform_capabilities".to_string(),
        "check_biometric_availability".to_string(),
        "authenticate_with_biometric".to_string(),
        "save_secure_data".to_string(),
        "get_secure_data".to_string(),
        "remove_secure_data".to_string(),
        "clear_secure_storage".to_string(),
        "get_secure_storage_keys".to_string(),
        "get_mobile_status".to_string(),
    ]
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_mobile_commands() {
        let commands = get_mobile_commands();
        assert!(!commands.is_empty());
        assert!(commands.contains(&"initialize_mobile_feature_manager".to_string()));
        assert!(commands.contains(&"authenticate_with_biometric".to_string()));
        assert!(commands.contains(&"check_biometric_availability".to_string()));
        assert!(commands.contains(&"save_secure_data".to_string()));
        assert!(commands.contains(&"get_secure_data".to_string()));
    }

    #[tokio::test]
    async fn test_biometric_availability_non_mobile() {
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            // 在非移动平台上测试生物识别可用性检查
            let app = tauri::test::mock_app();
            let result = check_biometric_availability(app.handle().clone()).await;
            assert!(result.is_ok());
            let status = result.unwrap();
            assert!(!status.is_available);
            assert!(status.error.is_some());
        }
    }

    #[tokio::test]
    async fn test_biometric_authentication_non_mobile() {
        #[cfg(not(any(target_os = "android", target_os = "ios")))]
        {
            // 在非移动平台上测试生物识别认证
            let app = tauri::test::mock_app();
            let result = authenticate_with_biometric(
                "测试认证".to_string(),
                app.handle().clone()
            ).await;
            assert!(result.is_err());
            assert!(result.unwrap_err().contains("不支持生物识别认证"));
        }
    }
} 