// Stronghold 安全存储前端接口
// 提供与 Rust 后端 Stronghold 服务交互的 TypeScript 接口

import { invoke } from '@tauri-apps/api/core';

/**
 * Stronghold 服务状态
 */
export interface StrongholdServiceStatus {
  /** 是否已初始化 */
  initialized: boolean;
  /** 当前状态描述 */
  status: string;
  /** 可用的后端类型 */
  available_backends: string[];
  /** 当前活跃后端 */
  active_backend?: string;
  /** 故障转移次数 */
  failover_count: number;
  /** 操作统计 */
  operation_stats: Record<string, number>;
}

/**
 * Stronghold 初始化参数
 */
export interface StrongholdInitParams {
  /** 主密码 */
  password: string;
  /** 配置类型 ("default", "high_security", "fast") */
  config_type?: string;
  /** 自定义配置 */
  custom_config?: any;
  /** 是否启用故障转移 */
  enable_failover?: boolean;
  /** 主后端类型 */
  primary_backend?: string;
}

/**
 * 存储操作参数
 */
export interface StoreParams {
  /** 存储键 */
  key: string;
  /** 存储值（Base64编码） */
  value: string;
  /** 数据类型 */
  item_type?: string;
}

/**
 * 获取操作参数
 */
export interface GetParams {
  /** 存储键 */
  key: string;
}

/**
 * 后端健康状态
 */
export interface BackendHealth {
  /** 后端类型 */
  backend: string;
  /** 状态 */
  status: string;
  /** 响应时间（毫秒） */
  response_time_ms: number;
  /** 最后检查时间（秒前） */
  last_check: number;
  /** 错误计数 */
  error_count: number;
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  /** 总条目数 */
  total_entries: number;
  /** 总访问次数 */
  total_access_count: number;
  /** 平均访问次数 */
  avg_access_count: number;
  /** 缓存大小限制 */
  cache_size_limit: number;
}

/**
 * Stronghold 能力信息
 */
export interface StrongholdCapabilities {
  /** Stronghold 是否可用 */
  stronghold_available: boolean;
  /** Keychain 是否可用 */
  keychain_available: boolean;
  /** 是否支持故障转移 */
  failover_supported: boolean;
  /** 支持的后端类型 */
  supported_backends: string[];
}

/**
 * Stronghold 安全存储服务类
 */
export class StrongholdService {
  private static instance: StrongholdService;
  private initialized = false;

  private constructor() {}

  /**
   * 获取单例实例
   */
  public static getInstance(): StrongholdService {
    if (!StrongholdService.instance) {
      StrongholdService.instance = new StrongholdService();
    }
    return StrongholdService.instance;
  }

  /**
   * 初始化 Stronghold 服务
   */
  async initialize(params: StrongholdInitParams): Promise<StrongholdServiceStatus> {
    try {
      const status = await invoke<StrongholdServiceStatus>('initialize_stronghold_service', params);
      this.initialized = status.initialized;
      console.log('Stronghold 服务初始化成功:', status);
      return status;
    } catch (error) {
      console.error('Stronghold 服务初始化失败:', error);
      throw error;
    }
  }

  /**
   * 获取服务状态
   */
  async getStatus(): Promise<StrongholdServiceStatus> {
    try {
      return await invoke<StrongholdServiceStatus>('get_stronghold_service_status');
    } catch (error) {
      console.error('获取 Stronghold 服务状态失败:', error);
      throw error;
    }
  }

  /**
   * 存储数据
   */
  async store(key: string, value: string | Uint8Array, itemType?: string): Promise<void> {
    try {
      // 如果是字符串，转换为 Uint8Array
      const bytes = typeof value === 'string' ? new TextEncoder().encode(value) : value;
      
      // 转换为 Base64
      const base64Value = btoa(String.fromCharCode(...bytes));

      const params: StoreParams = {
        key,
        value: base64Value,
        item_type: itemType,
      };

      await invoke('stronghold_store', params);
      console.log(`成功存储数据: ${key}`);
    } catch (error) {
      console.error(`存储数据失败 (${key}):`, error);
      throw error;
    }
  }

  /**
   * 获取数据
   */
  async get(key: string): Promise<Uint8Array | null> {
    try {
      const params: GetParams = { key };
      const base64Value = await invoke<string | null>('stronghold_get', params);

      if (base64Value === null) {
        return null;
      }

      // 从 Base64 解码
      const binaryString = atob(base64Value);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      console.log(`成功获取数据: ${key}`);
      return bytes;
    } catch (error) {
      console.error(`获取数据失败 (${key}):`, error);
      throw error;
    }
  }

  /**
   * 获取数据作为字符串
   */
  async getString(key: string): Promise<string | null> {
    const bytes = await this.get(key);
    if (bytes === null) {
      return null;
    }
    return new TextDecoder().decode(bytes);
  }

  /**
   * 删除数据
   */
  async remove(key: string): Promise<boolean> {
    try {
      const params: GetParams = { key };
      const removed = await invoke<boolean>('stronghold_remove', params);
      console.log(`删除数据: ${key}, 结果: ${removed}`);
      return removed;
    } catch (error) {
      console.error(`删除数据失败 (${key}):`, error);
      throw error;
    }
  }

  /**
   * 列出所有键
   */
  async listKeys(): Promise<string[]> {
    try {
      const keys = await invoke<string[]>('stronghold_list_keys');
      console.log(`列出 ${keys.length} 个键`);
      return keys;
    } catch (error) {
      console.error('列出键失败:', error);
      throw error;
    }
  }

  /**
   * 检查键是否存在
   */
  async exists(key: string): Promise<boolean> {
    try {
      const params: GetParams = { key };
      const exists = await invoke<boolean>('stronghold_exists', params);
      console.log(`检查键存在性: ${key} = ${exists}`);
      return exists;
    } catch (error) {
      console.error(`检查键存在性失败 (${key}):`, error);
      throw error;
    }
  }

  /**
   * 切换后端
   */
  async switchBackend(backend: 'stronghold' | 'keychain'): Promise<void> {
    try {
      await invoke('stronghold_switch_backend', { backend });
      console.log(`成功切换到后端: ${backend}`);
    } catch (error) {
      console.error(`切换后端失败 (${backend}):`, error);
      throw error;
    }
  }

  /**
   * 获取后端健康状态
   */
  async getBackendHealth(): Promise<BackendHealth[]> {
    try {
      return await invoke<BackendHealth[]>('stronghold_get_backend_health');
    } catch (error) {
      console.error('获取后端健康状态失败:', error);
      throw error;
    }
  }

  /**
   * 重置服务
   */
  async reset(): Promise<void> {
    try {
      await invoke('reset_stronghold_service');
      this.initialized = false;
      console.log('Stronghold 服务重置成功');
    } catch (error) {
      console.error('重置 Stronghold 服务失败:', error);
      throw error;
    }
  }

  /**
   * 检查能力
   */
  async checkCapabilities(): Promise<StrongholdCapabilities> {
    try {
      return await invoke<StrongholdCapabilities>('check_stronghold_capabilities');
    } catch (error) {
      console.error('检查 Stronghold 能力失败:', error);
      throw error;
    }
  }

  /**
   * 获取缓存统计
   */
  async getCacheStats(): Promise<CacheStats> {
    try {
      return await invoke<CacheStats>('get_stronghold_cache_stats');
    } catch (error) {
      console.error('获取缓存统计失败:', error);
      throw error;
    }
  }

  /**
   * 清理缓存
   */
  async cleanupCache(maxAgeSecs?: number): Promise<void> {
    try {
      await invoke('cleanup_stronghold_cache', { max_age_secs: maxAgeSecs });
      console.log('缓存清理成功');
    } catch (error) {
      console.error('清理缓存失败:', error);
      throw error;
    }
  }

  /**
   * 检查是否已初始化
   */
  isInitialized(): boolean {
    return this.initialized;
  }
}

/**
 * 获取 Stronghold 服务实例
 */
export const strongholdService = StrongholdService.getInstance();

/**
 * 工具函数：创建默认初始化参数
 */
export function createDefaultInitParams(password: string): StrongholdInitParams {
  return {
    password,
    config_type: 'default',
    enable_failover: true,
    primary_backend: 'stronghold',
  };
}

/**
 * 工具函数：创建高安全性初始化参数
 */
export function createHighSecurityInitParams(password: string): StrongholdInitParams {
  return {
    password,
    config_type: 'high_security',
    enable_failover: false, // 高安全性模式不使用故障转移
    primary_backend: 'stronghold',
  };
}

/**
 * 工具函数：创建快速初始化参数（开发环境）
 */
export function createFastInitParams(password: string): StrongholdInitParams {
  return {
    password,
    config_type: 'fast',
    enable_failover: true,
    primary_backend: 'stronghold',
  };
}

// 导出默认实例
export default strongholdService; 