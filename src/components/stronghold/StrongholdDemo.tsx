// Stronghold 安全存储演示组件
// 展示如何使用 Stronghold 服务进行安全存储操作

import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Space, Typography, Alert, Divider, Table, Tag, Spin } from 'antd';
import { 
  SafetyOutlined, 
  KeyOutlined, 
  DatabaseOutlined, 
  SettingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  DeleteOutlined
} from '@ant-design/icons';

import { 
  strongholdService, 
  createDefaultInitParams,
  createHighSecurityInitParams,
  createFastInitParams,
  type StrongholdServiceStatus,
  type BackendHealth,
  type CacheStats,
  type StrongholdCapabilities
} from '../../lib/stronghold';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;

/**
 * Stronghold 演示组件
 */
export const StrongholdDemo: React.FC = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState<StrongholdServiceStatus | null>(null);
  const [capabilities, setCapabilities] = useState<StrongholdCapabilities | null>(null);
  const [backendHealth, setBackendHealth] = useState<BackendHealth[]>([]);
  const [cacheStats, setCacheStats] = useState<CacheStats | null>(null);
  
  // 表单状态
  const [password, setPassword] = useState('demo_password_123!');
  const [configType, setConfigType] = useState<'default' | 'high_security' | 'fast'>('default');
  const [storeKey, setStoreKey] = useState('demo_key');
  const [storeValue, setStoreValue] = useState('Hello, Stronghold!');
  const [getKey, setGetKey] = useState('demo_key');
  const [retrievedValue, setRetrievedValue] = useState<string | null>(null);
  const [keysList, setKeysList] = useState<string[]>([]);

  // 错误和成功消息
  const [message, setMessage] = useState<{ type: 'success' | 'error' | 'info'; content: string } | null>(null);

  /**
   * 显示消息
   */
  const showMessage = (type: 'success' | 'error' | 'info', content: string) => {
    setMessage({ type, content });
    setTimeout(() => setMessage(null), 5000);
  };

  /**
   * 刷新状态信息
   */
  const refreshStatus = async () => {
    try {
      const [statusData, capabilitiesData, healthData, cacheData] = await Promise.all([
        strongholdService.getStatus(),
        strongholdService.checkCapabilities().catch(() => null),
        strongholdService.getBackendHealth().catch(() => []),
        strongholdService.getCacheStats().catch(() => null),
      ]);

      setStatus(statusData);
      setCapabilities(capabilitiesData);
      setBackendHealth(healthData);
      setCacheStats(cacheData);
    } catch (error) {
      console.error('刷新状态失败:', error);
    }
  };

  /**
   * 初始化 Stronghold 服务
   */
  const handleInitialize = async () => {
    setLoading(true);
    try {
      let initParams;
      switch (configType) {
        case 'high_security':
          initParams = createHighSecurityInitParams(password);
          break;
        case 'fast':
          initParams = createFastInitParams(password);
          break;
        default:
          initParams = createDefaultInitParams(password);
      }

      const result = await strongholdService.initialize(initParams);
      setStatus(result);
      showMessage('success', `Stronghold 服务初始化成功 (${configType} 模式)`);
      await refreshStatus();
    } catch (error) {
      showMessage('error', `初始化失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 存储数据
   */
  const handleStore = async () => {
    if (!storeKey || !storeValue) {
      showMessage('error', '请输入键和值');
      return;
    }

    setLoading(true);
    try {
      await strongholdService.store(storeKey, storeValue, 'demo_data');
      showMessage('success', `成功存储数据: ${storeKey}`);
      await refreshKeysList();
    } catch (error) {
      showMessage('error', `存储失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 获取数据
   */
  const handleGet = async () => {
    if (!getKey) {
      showMessage('error', '请输入要获取的键');
      return;
    }

    setLoading(true);
    try {
      const value = await strongholdService.getString(getKey);
      setRetrievedValue(value);
      if (value !== null) {
        showMessage('success', `成功获取数据: ${getKey}`);
      } else {
        showMessage('info', `键不存在: ${getKey}`);
      }
    } catch (error) {
      showMessage('error', `获取失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 删除数据
   */
  const handleRemove = async (key: string) => {
    setLoading(true);
    try {
      const removed = await strongholdService.remove(key);
      if (removed) {
        showMessage('success', `成功删除数据: ${key}`);
        await refreshKeysList();
        if (key === getKey) {
          setRetrievedValue(null);
        }
      } else {
        showMessage('info', `键不存在: ${key}`);
      }
    } catch (error) {
      showMessage('error', `删除失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 刷新键列表
   */
  const refreshKeysList = async () => {
    try {
      const keys = await strongholdService.listKeys();
      setKeysList(keys);
    } catch (error) {
      console.error('刷新键列表失败:', error);
    }
  };

  /**
   * 切换后端
   */
  const handleSwitchBackend = async (backend: 'stronghold' | 'keychain') => {
    setLoading(true);
    try {
      await strongholdService.switchBackend(backend);
      showMessage('success', `成功切换到 ${backend} 后端`);
      await refreshStatus();
    } catch (error) {
      showMessage('error', `切换后端失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 重置服务
   */
  const handleReset = async () => {
    setLoading(true);
    try {
      await strongholdService.reset();
      setStatus(null);
      setKeysList([]);
      setRetrievedValue(null);
      showMessage('success', 'Stronghold 服务重置成功');
    } catch (error) {
      showMessage('error', `重置失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  /**
   * 清理缓存
   */
  const handleCleanupCache = async () => {
    setLoading(true);
    try {
      await strongholdService.cleanupCache(3600); // 清理1小时前的缓存
      showMessage('success', '缓存清理成功');
      await refreshStatus();
    } catch (error) {
      showMessage('error', `缓存清理失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时刷新状态
  useEffect(() => {
    refreshStatus();
  }, []);

  // 如果服务已初始化，定期刷新键列表
  useEffect(() => {
    if (status?.initialized) {
      refreshKeysList();
    }
  }, [status?.initialized]);

  // 后端健康状态表格列定义
  const healthColumns = [
    {
      title: '后端',
      dataIndex: 'backend',
      key: 'backend',
      render: (backend: string) => (
        <Tag color={backend === 'Stronghold' ? 'blue' : 'green'}>
          {backend}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <Tag color={status === 'Available' ? 'success' : 'error'}>
          {status}
        </Tag>
      ),
    },
    {
      title: '响应时间',
      dataIndex: 'response_time_ms',
      key: 'response_time_ms',
      render: (time: number) => `${time}ms`,
    },
    {
      title: '错误计数',
      dataIndex: 'error_count',
      key: 'error_count',
    },
  ];

  // 键列表表格列定义
  const keysColumns = [
    {
      title: '键名',
      dataIndex: 'key',
      key: 'key',
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: { key: string }) => (
        <Space>
          <Button 
            size="small" 
            onClick={() => {
              setGetKey(record.key);
              handleGet();
            }}
          >
            获取
          </Button>
          <Button 
            size="small" 
            danger 
            icon={<DeleteOutlined />}
            onClick={() => handleRemove(record.key)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  const keysData = keysList.map(key => ({ key }));

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>
        <SafetyOutlined /> Stronghold 安全存储演示
      </Title>
      
      <Paragraph>
        这个演示展示了如何使用 Stronghold 安全存储服务进行数据的安全存储、检索和管理。
        Stronghold 基于 IOTA 的军用级加密技术，提供硬件级安全保护。
      </Paragraph>

      {message && (
        <Alert
          message={message.content}
          type={message.type}
          showIcon
          closable
          style={{ marginBottom: '16px' }}
        />
      )}

      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        {/* 服务状态 */}
        <Card title={<><SettingOutlined /> 服务状态</>}>
          <Space direction="vertical" style={{ width: '100%' }}>
            {status ? (
              <div>
                <Space>
                  <Tag color={status.initialized ? 'success' : 'error'}>
                    {status.initialized ? '已初始化' : '未初始化'}
                  </Tag>
                  <Text>状态: {status.status}</Text>
                  {status.active_backend && (
                    <Text>活跃后端: <Tag>{status.active_backend}</Tag></Text>
                  )}
                  <Text>故障转移次数: {status.failover_count}</Text>
                </Space>
                <div style={{ marginTop: '8px' }}>
                  <Text>可用后端: </Text>
                  {status.available_backends.map(backend => (
                    <Tag key={backend} color="blue">{backend}</Tag>
                  ))}
                </div>
              </div>
            ) : (
              <Text type="secondary">服务未初始化</Text>
            )}
            
            <Space>
              <Button icon={<ReloadOutlined />} onClick={refreshStatus}>
                刷新状态
              </Button>
              {status?.initialized && (
                <>
                  <Button onClick={() => handleSwitchBackend('stronghold')}>
                    切换到 Stronghold
                  </Button>
                  <Button onClick={() => handleSwitchBackend('keychain')}>
                    切换到 Keychain
                  </Button>
                  <Button onClick={handleCleanupCache}>
                    清理缓存
                  </Button>
                  <Button danger onClick={handleReset}>
                    重置服务
                  </Button>
                </>
              )}
            </Space>
          </Space>
        </Card>

        {/* 服务初始化 */}
        {!status?.initialized && (
          <Card title={<><KeyOutlined /> 初始化服务</>}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>主密码:</Text>
                <Input.Password
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="输入主密码"
                  style={{ marginTop: '8px' }}
                />
              </div>
              
              <div>
                <Text strong>配置类型:</Text>
                <Space style={{ marginTop: '8px' }}>
                  <Button 
                    type={configType === 'default' ? 'primary' : 'default'}
                    onClick={() => setConfigType('default')}
                  >
                    默认配置
                  </Button>
                  <Button 
                    type={configType === 'high_security' ? 'primary' : 'default'}
                    onClick={() => setConfigType('high_security')}
                  >
                    高安全性
                  </Button>
                  <Button 
                    type={configType === 'fast' ? 'primary' : 'default'}
                    onClick={() => setConfigType('fast')}
                  >
                    快速配置
                  </Button>
                </Space>
              </div>

              <Button 
                type="primary" 
                loading={loading}
                onClick={handleInitialize}
                icon={<CheckCircleOutlined />}
              >
                初始化 Stronghold 服务
              </Button>
            </Space>
          </Card>
        )}

        {/* 数据操作 */}
        {status?.initialized && (
          <>
            <Card title={<><DatabaseOutlined /> 数据操作</>}>
              <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                {/* 存储数据 */}
                <div>
                  <Title level={4}>存储数据</Title>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Input
                      placeholder="键名"
                      value={storeKey}
                      onChange={(e) => setStoreKey(e.target.value)}
                      prefix={<KeyOutlined />}
                    />
                    <TextArea
                      placeholder="要存储的值"
                      value={storeValue}
                      onChange={(e) => setStoreValue(e.target.value)}
                      rows={3}
                    />
                    <Button 
                      type="primary" 
                      loading={loading}
                      onClick={handleStore}
                    >
                      存储数据
                    </Button>
                  </Space>
                </div>

                <Divider />

                {/* 获取数据 */}
                <div>
                  <Title level={4}>获取数据</Title>
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Input
                      placeholder="要获取的键名"
                      value={getKey}
                      onChange={(e) => setGetKey(e.target.value)}
                      prefix={<KeyOutlined />}
                    />
                    <Button 
                      type="primary" 
                      loading={loading}
                      onClick={handleGet}
                    >
                      获取数据
                    </Button>
                    {retrievedValue !== null && (
                      <TextArea
                        value={retrievedValue}
                        readOnly
                        rows={3}
                        style={{ backgroundColor: '#f5f5f5' }}
                      />
                    )}
                  </Space>
                </div>
              </Space>
            </Card>

            {/* 存储的键列表 */}
            <Card title="存储的键列表">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button onClick={refreshKeysList} icon={<ReloadOutlined />}>
                  刷新列表
                </Button>
                <Table
                  columns={keysColumns}
                  dataSource={keysData}
                  size="small"
                  pagination={false}
                  locale={{ emptyText: '暂无存储的数据' }}
                />
              </Space>
            </Card>

            {/* 后端健康状态 */}
            {backendHealth.length > 0 && (
              <Card title="后端健康状态">
                <Table
                  columns={healthColumns}
                  dataSource={backendHealth}
                  size="small"
                  pagination={false}
                />
              </Card>
            )}

            {/* 系统信息 */}
            {(capabilities || cacheStats) && (
              <Card title="系统信息">
                <Space direction="vertical" style={{ width: '100%' }}>
                  {capabilities && (
                    <div>
                      <Title level={5}>系统能力</Title>
                      <Space>
                        <Tag color={capabilities.stronghold_available ? 'success' : 'error'}>
                          Stronghold: {capabilities.stronghold_available ? '可用' : '不可用'}
                        </Tag>
                        <Tag color={capabilities.keychain_available ? 'success' : 'error'}>
                          Keychain: {capabilities.keychain_available ? '可用' : '不可用'}
                        </Tag>
                        <Tag color={capabilities.failover_supported ? 'success' : 'default'}>
                          故障转移: {capabilities.failover_supported ? '支持' : '不支持'}
                        </Tag>
                      </Space>
                    </div>
                  )}

                  {cacheStats && (
                    <div>
                      <Title level={5}>缓存统计</Title>
                      <Space>
                        <Text>缓存条目: {cacheStats.total_entries}</Text>
                        <Text>总访问次数: {cacheStats.total_access_count}</Text>
                        <Text>平均访问次数: {cacheStats.avg_access_count.toFixed(2)}</Text>
                        <Text>缓存限制: {cacheStats.cache_size_limit}</Text>
                      </Space>
                    </div>
                  )}
                </Space>
              </Card>
            )}
          </>
        )}
      </Space>

      {loading && (
        <div style={{ 
          position: 'fixed', 
          top: 0, 
          left: 0, 
          right: 0, 
          bottom: 0, 
          backgroundColor: 'rgba(0,0,0,0.1)', 
          display: 'flex', 
          justifyContent: 'center', 
          alignItems: 'center',
          zIndex: 1000
        }}>
          <Spin size="large" />
        </div>
      )}
    </div>
  );
};

export default StrongholdDemo; 